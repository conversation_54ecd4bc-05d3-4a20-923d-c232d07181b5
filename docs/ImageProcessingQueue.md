# 图像处理队列实现说明

## 概述

在 `AnalysisTaskJob` 中实现了一个简单而高效的共享队列机制，用于管理图像处理任务。该实现使用 Google Guava 库提供幂等性保证，确保重复执行 job 时 image_ids 不会重复添加，并且控制队列最大容量为 20。

## 核心特性

### 1. 共享队列
- 使用 `ConcurrentLinkedQueue<String>` 作为线程安全的共享队列
- 最大容量限制为 20 个图像 ID
- 使用 `AtomicInteger` 精确跟踪队列大小

### 2. 幂等性机制
- 使用 Guava Cache 实现幂等性检查
- 缓存过期时间为 24 小时
- 最大缓存大小为 10,000 条记录
- 防止重复添加相同的 image_id

### 3. 队列管理
- 自动检查队列容量，防止溢出
- 批量幂等性检查，提高性能
- 详细的日志记录，便于监控和调试

## 实现细节

### 核心组件

```java
// 共享队列
private static final ConcurrentLinkedQueue<String> SHARED_QUEUE = new ConcurrentLinkedQueue<>();

// 队列大小计数器
private static final AtomicInteger QUEUE_SIZE = new AtomicInteger(0);

// 幂等性缓存
private static final Cache<String, Boolean> PROCESSED_CACHE = CacheBuilder.newBuilder()
        .maximumSize(10000)
        .expireAfterWrite(24, TimeUnit.HOURS)
        .build();
```

### 主要方法

#### `addToSharedQueue(List<String> imageIds, Long jobId)`
- 添加图像 ID 到共享队列
- 执行幂等性检查
- 控制队列容量不超过最大限制
- 返回实际添加的数量

#### `processQueuedImages(CaptionUserVO user, Long jobId)`
- 从队列中取出图像 ID 进行处理
- 最多处理 20 个图像
- 异常处理和日志记录

#### `getQueueStatus()`
- 获取队列状态信息
- 包含当前大小、最大容量、已处理数量

## 使用方式

### 基本流程

1. **获取未标注图像**：从数据库查询未标注的图像 ID
2. **添加到队列**：调用 `addToSharedQueue()` 方法，自动进行幂等性检查
3. **处理队列**：调用 `processQueuedImages()` 方法处理队列中的图像
4. **状态监控**：使用 `getQueueStatus()` 获取队列状态

### 配置选项

可以通过 `ImageProcessingQueueConfig` 类进行配置：

```yaml
aigc:
  image-processing:
    queue:
      max-size: 20                    # 最大队列大小
      idempotent-cache-max-size: 10000 # 幂等性缓存最大大小
      idempotent-cache-expire-hours: 24 # 缓存过期时间（小时）
      enabled: true                   # 是否启用队列功能
      verbose-logging: false          # 是否启用详细日志
```

## 监控和管理

### 队列状态监控

使用 `QueueMonitorUtil` 工具类进行监控：

```java
// 打印队列状态
QueueMonitorUtil.printQueueStatus();

// 检查队列健康状态
boolean healthy = QueueMonitorUtil.isQueueHealthy();

// 获取队列使用率
double usage = QueueMonitorUtil.getQueueUsagePercentage();

// 获取状态摘要
String summary = QueueMonitorUtil.getQueueSummary();
```

### 日志示例

```
[AnalysisTaskJob] Found 150 images not captioned by user 1
[AnalysisTaskJob] Idempotency check: 130 images already processed, 20 new images
[AnalysisTaskJob] Added 20 images to shared queue, current queue size: 20
[AnalysisTaskJob] Starting to process up to 20 images from queue
[AnalysisTaskJob] Processed 20 images from queue
```

## 性能特点

### 优势
1. **线程安全**：使用并发安全的数据结构
2. **内存高效**：使用 Guava Cache 自动管理内存
3. **高性能**：批量操作和原子操作
4. **简单可靠**：基于成熟的开源库实现

### 限制
1. **单机限制**：队列状态不跨实例共享
2. **内存存储**：重启后队列状态丢失
3. **容量固定**：最大队列大小为 20

## 扩展建议

### 分布式支持
如果需要跨实例共享队列状态，可以考虑：
- 使用 Redis 作为共享存储
- 使用消息队列（如 RocketMQ）
- 使用分布式缓存

### 持久化支持
如果需要队列状态持久化：
- 定期将队列状态保存到数据库
- 启动时从数据库恢复队列状态

### 动态配置
- 支持运行时调整队列大小
- 支持动态开启/关闭幂等性检查

## 测试

提供了完整的单元测试 `AnalysisTaskJobTest`，覆盖：
- 空队列处理
- 正常图像处理
- 幂等性验证
- 队列容量限制
- 状态查询

运行测试：
```bash
mvn test -Dtest=AnalysisTaskJobTest
```

## 总结

该实现提供了一个简单、高效、可靠的图像处理队列机制，满足了以下要求：
- ✅ 共享队列管理
- ✅ 幂等性保证
- ✅ 容量控制（最多20个）
- ✅ 线程安全
- ✅ 易于监控和调试

通过使用 Guava 等成熟库，避免了重复造轮子，提高了代码的可靠性和可维护性。
