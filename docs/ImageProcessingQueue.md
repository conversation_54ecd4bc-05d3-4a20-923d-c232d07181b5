# 图像处理任务系统实现说明

## 概述

在 `AnalysisTaskJob` 中实现了一个基于虚拟线程池的高效任务处理系统。该实现使用 Java 21 的虚拟线程池管理并发任务，配合无界阻塞队列和 Google Guava 缓存提供幂等性保证，确保重复执行 job 时 image_ids 不会重复添加。

## 核心特性

### 1. 虚拟线程池
- 使用 `Executors.newFixedThreadPool(20, Thread.ofVirtual().factory())` 创建固定大小的虚拟线程池
- 最多 20 个并发任务，由线程池自动管理
- 虚拟线程轻量级，适合 I/O 密集型任务

### 2. 无界任务队列
- 使用 `LinkedBlockingQueue<String>` 作为无界阻塞队列
- 不设置队列上限，避免任务丢失
- 线程安全，支持高并发操作

### 3. 幂等性机制
- 使用 Guava Cache 实现幂等性检查
- 缓存过期时间为 24 小时
- 最大缓存大小为 10,000 条记录
- 防止重复添加相同的 image_id

### 4. 自动并发控制
- 线程池自动管理并发数量，无需手动控制
- 任务提交后异步执行，提高响应性
- 详细的日志记录，便于监控和调试

## 实现细节

### 核心组件

```java
// 固定大小的虚拟线程池
private static final ExecutorService VIRTUAL_THREAD_POOL = Executors.newFixedThreadPool(20,
        Thread.ofVirtual().name("image-processing-", 0).factory());

// 无界阻塞队列
private static final BlockingQueue<String> TASK_QUEUE = new LinkedBlockingQueue<>();

// 幂等性缓存
private static final Cache<String, Boolean> PROCESSED_CACHE = CacheBuilder.newBuilder()
        .maximumSize(10000)
        .expireAfterWrite(24, TimeUnit.HOURS)
        .build();
```

### 主要方法

#### `addToTaskQueue(List<String> imageIds, Long jobId)`
- 添加图像 ID 到任务队列
- 执行幂等性检查
- 使用无界队列，不限制容量
- 返回实际添加的数量

#### `submitTasksToThreadPool(CaptionUserVO user, Long jobId)`
- 将队列中的任务提交到虚拟线程池
- 线程池自动管理并发数量（最多 20 个）
- 异步执行，提高响应性

#### `getTaskStatus()`
- 获取任务系统状态信息
- 包含队列大小、已处理数量、线程池状态

## 使用方式

### 基本流程

1. **获取未标注图像**：从数据库查询未标注的图像 ID
2. **添加到队列**：调用 `addToTaskQueue()` 方法，自动进行幂等性检查
3. **提交任务**：调用 `submitTasksToThreadPool()` 方法将任务提交到虚拟线程池
4. **并发处理**：虚拟线程池自动管理并发执行（最多 20 个并发）
5. **状态监控**：使用 `getTaskStatus()` 获取任务系统状态

### 配置选项

可以通过 `ImageProcessingQueueConfig` 类进行配置：

```yaml
aigc:
  image-processing:
    queue:
      thread-pool-size: 20            # 虚拟线程池大小
      idempotent-cache-max-size: 10000 # 幂等性缓存最大大小
      idempotent-cache-expire-hours: 24 # 缓存过期时间（小时）
      enabled: true                   # 是否启用任务系统
      verbose-logging: false          # 是否启用详细日志
```

## 监控和管理

### 队列状态监控

使用 `QueueMonitorUtil` 工具类进行监控：

```java
// 打印队列状态
QueueMonitorUtil.printQueueStatus();

// 检查队列健康状态
boolean healthy = QueueMonitorUtil.isQueueHealthy();

// 获取队列使用率
double usage = QueueMonitorUtil.getQueueUsagePercentage();

// 获取状态摘要
String summary = QueueMonitorUtil.getQueueSummary();
```

### 日志示例

```
[AnalysisTaskJob] Found 150 images not captioned by user 1
[AnalysisTaskJob] Idempotency check: 130 images already processed, 20 new images
[AnalysisTaskJob] Added 20 images to shared queue, current queue size: 20
[AnalysisTaskJob] Starting to process up to 20 images from queue
[AnalysisTaskJob] Processed 20 images from queue
```

## 性能特点

### 优势
1. **线程安全**：使用并发安全的数据结构
2. **内存高效**：使用 Guava Cache 自动管理内存
3. **高性能**：批量操作和原子操作
4. **简单可靠**：基于成熟的开源库实现

### 限制
1. **单机限制**：队列状态不跨实例共享
2. **内存存储**：重启后队列状态丢失
3. **容量固定**：最大队列大小为 20

## 扩展建议

### 分布式支持
如果需要跨实例共享队列状态，可以考虑：
- 使用 Redis 作为共享存储
- 使用消息队列（如 RocketMQ）
- 使用分布式缓存

### 持久化支持
如果需要队列状态持久化：
- 定期将队列状态保存到数据库
- 启动时从数据库恢复队列状态

### 动态配置
- 支持运行时调整队列大小
- 支持动态开启/关闭幂等性检查

## 测试

提供了完整的单元测试 `AnalysisTaskJobTest`，覆盖：
- 空队列处理
- 正常图像处理
- 幂等性验证
- 队列容量限制
- 状态查询

运行测试：
```bash
mvn test -Dtest=AnalysisTaskJobTest
```

## 总结

该实现提供了一个简单、高效、可靠的图像处理队列机制，满足了以下要求：
- ✅ 共享队列管理
- ✅ 幂等性保证
- ✅ 容量控制（最多20个）
- ✅ 线程安全
- ✅ 易于监控和调试

通过使用 Guava 等成熟库，避免了重复造轮子，提高了代码的可靠性和可维护性。
