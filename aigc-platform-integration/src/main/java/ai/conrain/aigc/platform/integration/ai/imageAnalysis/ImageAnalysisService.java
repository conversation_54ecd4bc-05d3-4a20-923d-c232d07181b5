package ai.conrain.aigc.platform.integration.ai.imageAnalysis;

import ai.conrain.aigc.platform.integration.ai.model.ImageAnalysisCaption;
import ai.conrain.aigc.platform.integration.ai.model.ImageAnalysisResult;
import ai.conrain.aigc.platform.integration.ai.model.ImageAnalysisTaskResponse;
import ai.conrain.aigc.platform.integration.aliyun.OssService;
import ai.conrain.aigc.platform.integration.utils.IntegrationUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import java.util.function.Consumer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

/**
 * 图像分析服务实现类
 * 调用外部OpenAPI进行图像打标分析
 * https://conrain.yuque.com/org-wiki-conrain-pcgdb4/icf3gp/ih2z7d775fuo2rnv?singleDoc#
 */
@Slf4j
@Service
public class ImageAnalysisService {

    @Value("${ai.imageAnalysis.baseUrl}")
    private String BASE_URL;

    @Value("${ai.imageAnalysis.apiKey}")
    private String API_KEY = "${apikey}";

    @Autowired
    @Qualifier("extraLongRestTemplate")
    private RestTemplate restTemplate;

    @Autowired
    private OssService ossService;

    public ImageAnalysisTaskResponse createAnalysisTask(String imageUrl) {
        return this.createAnalysisTask(imageUrl, "gemini-flash", false, true, null);
    }

    public ImageAnalysisTaskResponse createAnalysisTask(String imageUrl, boolean clothOnly) {
        return this.createAnalysisTask(imageUrl, "gemini-flash", clothOnly, true, null);
    }

    public ImageAnalysisTaskResponse createAnalysisTask(String imageUrl, String model, boolean clothOnly,
                                                        boolean useGenreClassifyModel, String clothType) {
        if (StringUtils.isBlank(imageUrl)) {
            throw new IllegalArgumentException("imageUrl must not be blank");
        }
        if (StringUtils.isBlank(model)) {
            model = "gemini-flash";
        }

        try {
            // 构建请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("Authorization", "Bearer " + API_KEY);

            // 构建请求体
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("image_input", ossService.resize1024(imageUrl));
            requestBody.put("model", model);
            requestBody.put("cloth_only", clothOnly);
            requestBody.put("use_intended_use_classifier", useGenreClassifyModel);
            if (StringUtils.isNotBlank(clothType)) {
                requestBody.put("cloth_type", clothType);
            }

            // 发送HTTP请求
            HttpEntity<String> requestEntity = new HttpEntity<>(JSON.toJSONString(requestBody), headers);

            String CREATE_TASK_URL = BASE_URL + "/tasks";
            ResponseEntity<String> responseEntity = restTemplate.exchange(
                    CREATE_TASK_URL,
                    HttpMethod.POST,
                    requestEntity,
                    String.class);

            if (responseEntity.getStatusCode().is2xxSuccessful()) {
                String responseBody = responseEntity.getBody();
                log.info("createAnalysisTask response: {}", responseBody);

                JSONObject jsonResponse = JSON.parseObject(responseBody);
                ImageAnalysisTaskResponse response = new ImageAnalysisTaskResponse();
                response.setTaskId(jsonResponse.getString("task_id"));
                response.setStatus(jsonResponse.getString("status"));
                response.setMessage(jsonResponse.getString("message"));
                response.setCreatedAt(jsonResponse.getString("created_at"));

                return response;
            } else {
                log.error("createAnalysisTask error: status={}, response={}",
                        responseEntity.getStatusCodeValue(), responseEntity.getBody());
                throw new RuntimeException("Image analysis task creation failed: " + responseEntity.getBody());
            }
        } catch (Exception e) {
            log.error("createAnalysisTask error", e);
            throw new RuntimeException("Image analysis task creation error: " + e.getMessage());
        }
    }

    public ImageAnalysisResult getAnalysisResult(String taskId) {
        if (StringUtils.isBlank(taskId)) {
            throw new IllegalArgumentException("taskId must not be blank");
        }

        ImageAnalysisResult result = new ImageAnalysisResult();
        result.setTaskId(taskId);

        getAnalysisResult(taskId, (jsonResponse ) -> {
            result.setStatus(jsonResponse.getString("status"));

            // 如果任务完成，解析分析结果
            if ("completed".equals(jsonResponse.getString("status"))) {
                ImageAnalysisCaption analysis = jsonResponse.getJSONObject("result").getObject("analysis",
                    ImageAnalysisCaption.class);
                // 处理"None"字符串，转换为null
                IntegrationUtils.processNoneToNull(analysis);
                result.setAnalysis(analysis);
            } else if ("failed".equals(jsonResponse.getString("status"))) {
                result.setErrorMessage(jsonResponse.getString("error"));
            }
        });

        return result;
    }

    public Map<String, Object> getAnalysisResult(String taskId, Consumer<JSONObject> onJsonResponse) {
        if (StringUtils.isBlank(taskId)) {
            throw new IllegalArgumentException("taskId must not be blank");
        }

        try {
            // 构建请求头
            HttpHeaders headers = new HttpHeaders();
            headers.set("Authorization", "Bearer " + API_KEY);

            // 发送HTTP请求
            HttpEntity<String> requestEntity = new HttpEntity<>(headers);

            String GET_TASK_URL = BASE_URL + "/tasks/";
            ResponseEntity<String> responseEntity = restTemplate.exchange(
                GET_TASK_URL + taskId,
                HttpMethod.GET,
                requestEntity,
                String.class);

            if (responseEntity.getStatusCode().is2xxSuccessful()) {
                String responseBody = responseEntity.getBody();
                log.info("getAnalysisResult response: {}", responseBody);

                JSONObject jsonResponse = JSON.parseObject(responseBody);

                JSONObject result = jsonResponse.getJSONObject("result");

                onJsonResponse.accept(jsonResponse);

                return result;
            } else {
                log.error("getAnalysisResult error: status={}, response={}",
                    responseEntity.getStatusCodeValue(), responseEntity.getBody());
                throw new RuntimeException("Get analysis result failed: " + responseEntity.getBody());
            }
        } catch (Exception e) {
            log.error("getAnalysisResult error", e);
            throw new RuntimeException("Get analysis result error: " + e.getMessage());
        }
    }
}