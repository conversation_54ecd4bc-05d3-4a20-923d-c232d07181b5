package ai.conrain.aigc.platform.service.component.queue;

import ai.conrain.aigc.platform.service.component.event.ImageProcessingEventPublisher;
import ai.conrain.aigc.platform.service.component.idempotent.IdempotentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.locks.ReentrantReadWriteLock;
import java.util.List;
import java.util.ArrayList;
import java.util.Set;
import java.util.HashSet;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 共享图像处理队列管理器
 * 提供线程安全的队列操作，支持幂等性检查和数量限制控制
 */
@Slf4j
@Component
public class SharedImageProcessingQueue {

    /**
     * 最大队列容量
     */
    private static final int MAX_QUEUE_SIZE = 20;

    /**
     * 幂等性TTL（秒）- 24小时
     */
    private static final long IDEMPOTENT_TTL_SECONDS = 24 * 60 * 60;

    @Autowired
    private IdempotentService idempotentService;

    @Autowired
    private ImageProcessingEventPublisher eventPublisher;
    
    /**
     * 待处理的图像ID队列
     */
    private final ConcurrentLinkedQueue<String> processingQueue = new ConcurrentLinkedQueue<>();
    
    /**
     * 已处理的图像ID集合，用于幂等性检查
     */
    private final ConcurrentHashMap<String, ProcessingRecord> processedImages = new ConcurrentHashMap<>();
    
    /**
     * 正在处理的图像ID集合
     */
    private final ConcurrentHashMap<String, ProcessingRecord> processingImages = new ConcurrentHashMap<>();
    
    /**
     * 当前队列大小计数器
     */
    private final AtomicInteger currentSize = new AtomicInteger(0);
    
    /**
     * 读写锁，用于保护队列操作
     */
    private final ReentrantReadWriteLock lock = new ReentrantReadWriteLock();
    
    /**
     * 处理记录
     */
    public static class ProcessingRecord {
        private final String imageId;
        private final LocalDateTime timestamp;
        private final String jobId;
        private ProcessingStatus status;
        
        public ProcessingRecord(String imageId, String jobId) {
            this.imageId = imageId;
            this.jobId = jobId;
            this.timestamp = LocalDateTime.now();
            this.status = ProcessingStatus.PENDING;
        }
        
        // Getters and setters
        public String getImageId() { return imageId; }
        public LocalDateTime getTimestamp() { return timestamp; }
        public String getJobId() { return jobId; }
        public ProcessingStatus getStatus() { return status; }
        public void setStatus(ProcessingStatus status) { this.status = status; }
    }
    
    /**
     * 处理状态枚举
     */
    public enum ProcessingStatus {
        PENDING,    // 待处理
        PROCESSING, // 处理中
        COMPLETED,  // 已完成
        FAILED      // 失败
    }
    
    /**
     * 添加图像ID到队列（支持幂等性）
     * 
     * @param imageIds 图像ID列表
     * @param jobId 任务ID
     * @return 实际添加的图像ID数量
     */
    public int addImageIds(List<String> imageIds, String jobId) {
        if (imageIds == null || imageIds.isEmpty()) {
            return 0;
        }
        
        lock.writeLock().lock();
        try {
            int addedCount = 0;
            List<String> toAdd = new ArrayList<>();
            
            // 检查幂等性和队列容量
            for (String imageId : imageIds) {
                if (currentSize.get() >= MAX_QUEUE_SIZE) {
                    log.warn("[SharedImageProcessingQueue] Queue is full, cannot add more images. Current size: {}", currentSize.get());
                    break;
                }
                
                // 幂等性检查：如果已经处理过或正在处理，则跳过
                if (isAlreadyProcessedOrProcessing(imageId)) {
                    log.debug("[SharedImageProcessingQueue] Image {} already processed or processing, skipping", imageId);
                    continue;
                }
                
                toAdd.add(imageId);
            }
            
            // 批量添加到队列
            for (String imageId : toAdd) {
                ProcessingRecord record = new ProcessingRecord(imageId, jobId);
                processingQueue.offer(imageId);
                processedImages.put(imageId, record);
                currentSize.incrementAndGet();
                addedCount++;
                
                log.debug("[SharedImageProcessingQueue] Added image {} to queue, job: {}", imageId, jobId);
            }
            
            log.info("[SharedImageProcessingQueue] Added {} images to queue, current size: {}", addedCount, currentSize.get());
            return addedCount;
            
        } finally {
            lock.writeLock().unlock();
        }
    }
    
    /**
     * 从队列中获取下一个待处理的图像ID
     * 
     * @return 图像ID，如果队列为空则返回null
     */
    public String pollNextImageId() {
        lock.writeLock().lock();
        try {
            String imageId = processingQueue.poll();
            if (imageId != null) {
                currentSize.decrementAndGet();
                
                // 移动到正在处理集合
                ProcessingRecord record = processedImages.get(imageId);
                if (record != null) {
                    record.setStatus(ProcessingStatus.PROCESSING);
                    processingImages.put(imageId, record);
                }
                
                log.debug("[SharedImageProcessingQueue] Polled image {} from queue, remaining size: {}", imageId, currentSize.get());
            }
            return imageId;
        } finally {
            lock.writeLock().unlock();
        }
    }
    
    /**
     * 标记图像处理完成
     * 
     * @param imageId 图像ID
     * @param success 是否成功
     */
    public void markImageCompleted(String imageId, boolean success) {
        lock.writeLock().lock();
        try {
            ProcessingRecord record = processingImages.remove(imageId);
            if (record != null) {
                record.setStatus(success ? ProcessingStatus.COMPLETED : ProcessingStatus.FAILED);
                log.debug("[SharedImageProcessingQueue] Marked image {} as {}", imageId, 
                         success ? "completed" : "failed");
            }
        } finally {
            lock.writeLock().unlock();
        }
    }
    
    /**
     * 检查图像是否已经处理过或正在处理
     * 
     * @param imageId 图像ID
     * @return true如果已处理或正在处理
     */
    private boolean isAlreadyProcessedOrProcessing(String imageId) {
        return processedImages.containsKey(imageId) || processingImages.containsKey(imageId);
    }
    
    /**
     * 获取当前队列大小
     * 
     * @return 队列大小
     */
    public int getCurrentSize() {
        return currentSize.get();
    }
    
    /**
     * 获取最大队列容量
     * 
     * @return 最大容量
     */
    public int getMaxSize() {
        return MAX_QUEUE_SIZE;
    }
    
    /**
     * 检查队列是否已满
     * 
     * @return true如果队列已满
     */
    public boolean isFull() {
        return currentSize.get() >= MAX_QUEUE_SIZE;
    }
    
    /**
     * 检查队列是否为空
     * 
     * @return true如果队列为空
     */
    public boolean isEmpty() {
        return currentSize.get() == 0;
    }
    
    /**
     * 获取队列状态信息
     * 
     * @return 状态信息
     */
    public QueueStatus getQueueStatus() {
        lock.readLock().lock();
        try {
            return new QueueStatus(
                currentSize.get(),
                MAX_QUEUE_SIZE,
                processedImages.size(),
                processingImages.size()
            );
        } finally {
            lock.readLock().unlock();
        }
    }
    
    /**
     * 队列状态信息
     */
    public static class QueueStatus {
        private final int pendingCount;
        private final int maxCapacity;
        private final int processedCount;
        private final int processingCount;
        
        public QueueStatus(int pendingCount, int maxCapacity, int processedCount, int processingCount) {
            this.pendingCount = pendingCount;
            this.maxCapacity = maxCapacity;
            this.processedCount = processedCount;
            this.processingCount = processingCount;
        }
        
        // Getters
        public int getPendingCount() { return pendingCount; }
        public int getMaxCapacity() { return maxCapacity; }
        public int getProcessedCount() { return processedCount; }
        public int getProcessingCount() { return processingCount; }
        
        @Override
        public String toString() {
            return String.format("QueueStatus{pending=%d, processing=%d, processed=%d, capacity=%d}", 
                               pendingCount, processingCount, processedCount, maxCapacity);
        }
    }
    
    /**
     * 清理过期的处理记录（可选的清理机制）
     * 
     * @param hoursToKeep 保留小时数
     */
    public void cleanupExpiredRecords(int hoursToKeep) {
        lock.writeLock().lock();
        try {
            LocalDateTime cutoffTime = LocalDateTime.now().minusHours(hoursToKeep);
            
            processedImages.entrySet().removeIf(entry -> {
                ProcessingRecord record = entry.getValue();
                return record.getTimestamp().isBefore(cutoffTime) && 
                       (record.getStatus() == ProcessingStatus.COMPLETED || 
                        record.getStatus() == ProcessingStatus.FAILED);
            });
            
            log.info("[SharedImageProcessingQueue] Cleaned up expired records, remaining: {}", processedImages.size());
        } finally {
            lock.writeLock().unlock();
        }
    }
}
