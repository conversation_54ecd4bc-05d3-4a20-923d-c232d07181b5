package ai.conrain.aigc.platform.service.component.event;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * 图像处理事件监听器
 */
@Slf4j
@Component
public class ImageProcessingEventListener {
    
    /**
     * 监听图像添加到队列事件
     */
    @EventListener
    @Async
    public void handleImageAddedToQueue(ImageProcessingEvent.ImageAddedToQueueEvent event) {
        log.info("[ImageProcessingEventListener] Image {} added to queue for job {}, current queue size: {}", 
                event.getImageId(), event.getJobId(), event.getQueueSize());
        
        // 可以在这里添加额外的处理逻辑，比如：
        // - 发送通知
        // - 更新统计信息
        // - 记录审计日志
    }
    
    /**
     * 监听图像开始处理事件
     */
    @EventListener
    @Async
    public void handleImageProcessingStarted(ImageProcessingEvent.ImageProcessingStartedEvent event) {
        log.info("[ImageProcessingEventListener] Started processing image {} for job {} at {}", 
                event.getImageId(), event.getJobId(), event.getTimestamp());
        
        // 可以在这里添加额外的处理逻辑，比如：
        // - 更新处理状态
        // - 启动监控
        // - 记录开始时间
    }
    
    /**
     * 监听图像处理完成事件
     */
    @EventListener
    @Async
    public void handleImageProcessingCompleted(ImageProcessingEvent.ImageProcessingCompletedEvent event) {
        if (event.isSuccess()) {
            log.info("[ImageProcessingEventListener] Successfully completed processing image {} for job {} at {}", 
                    event.getImageId(), event.getJobId(), event.getTimestamp());
        } else {
            log.error("[ImageProcessingEventListener] Failed to process image {} for job {} at {}, error: {}", 
                     event.getImageId(), event.getJobId(), event.getTimestamp(), event.getErrorMessage());
        }
        
        // 可以在这里添加额外的处理逻辑，比如：
        // - 更新数据库状态
        // - 发送完成通知
        // - 清理临时资源
        // - 记录处理结果
    }
    
    /**
     * 监听队列状态变化事件
     */
    @EventListener
    @Async
    public void handleQueueStatusChanged(ImageProcessingEvent.QueueStatusChangedEvent event) {
        log.info("[ImageProcessingEventListener] Queue status changed at {}: pending={}, processing={}, processed={}, capacity={}", 
                event.getTimestamp(), event.getCurrentSize(), event.getProcessingCount(), 
                event.getProcessedCount(), event.getMaxSize());
        
        // 队列状态监控逻辑
        if (event.getCurrentSize() >= event.getMaxSize() * 0.9) {
            log.warn("[ImageProcessingEventListener] Queue is nearly full: {}/{}", 
                    event.getCurrentSize(), event.getMaxSize());
        }
        
        if (event.getCurrentSize() == 0 && event.getProcessingCount() == 0) {
            log.info("[ImageProcessingEventListener] Queue is empty and no images are being processed");
        }
        
        // 可以在这里添加额外的处理逻辑，比如：
        // - 发送队列状态报告
        // - 触发自动扩容
        // - 更新监控指标
    }
    
    /**
     * 监听批量图像添加事件
     */
    @EventListener
    @Async
    public void handleBatchImagesAdded(ImageProcessingEvent.BatchImagesAddedEvent event) {
        log.info("[ImageProcessingEventListener] Batch images added at {}: added={}, skipped={}, jobId={}", 
                event.getTimestamp(), event.getAddedCount(), event.getSkippedCount(), event.getJobId());
        
        // 批量处理统计
        if (event.getSkippedCount() > 0) {
            log.info("[ImageProcessingEventListener] {} images were skipped due to idempotency check", 
                    event.getSkippedCount());
        }
        
        if (event.getAddedCount() == 0) {
            log.warn("[ImageProcessingEventListener] No new images were added to queue for job {}", 
                    event.getJobId());
        }
        
        // 可以在这里添加额外的处理逻辑，比如：
        // - 更新任务统计
        // - 发送批量处理报告
        // - 记录处理指标
    }
}
