package ai.conrain.aigc.platform.service.component.idempotent;

import java.util.List;
import java.util.Set;

/**
 * 幂等性服务接口
 */
public interface IdempotentService {
    
    /**
     * 检查操作是否已经执行过
     * 
     * @param key 幂等性键
     * @return true如果已经执行过
     */
    boolean isAlreadyProcessed(String key);
    
    /**
     * 标记操作已执行
     * 
     * @param key 幂等性键
     * @param ttlSeconds 过期时间（秒）
     */
    void markAsProcessed(String key, long ttlSeconds);
    
    /**
     * 批量检查操作是否已经执行过
     * 
     * @param keys 幂等性键列表
     * @return 已经执行过的键集合
     */
    Set<String> getAlreadyProcessedKeys(List<String> keys);
    
    /**
     * 批量标记操作已执行
     * 
     * @param keys 幂等性键列表
     * @param ttlSeconds 过期时间（秒）
     */
    void markAsProcessedBatch(List<String> keys, long ttlSeconds);
    
    /**
     * 删除幂等性记录
     * 
     * @param key 幂等性键
     */
    void removeProcessedRecord(String key);
    
    /**
     * 清理过期的幂等性记录
     */
    void cleanupExpiredRecords();
    
    /**
     * 获取幂等性记录数量
     * 
     * @return 记录数量
     */
    long getRecordCount();
}
