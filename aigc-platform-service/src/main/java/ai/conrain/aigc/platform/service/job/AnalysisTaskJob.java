package ai.conrain.aigc.platform.service.job;

import ai.conrain.aigc.platform.dal.pgsql.dao.ImageGroupDAO;
import ai.conrain.aigc.platform.integration.utils.EnvUtil;
import ai.conrain.aigc.platform.service.component.CaptionUserService;
import ai.conrain.aigc.platform.service.component.ImageGroupCaptionUserService;
import ai.conrain.aigc.platform.service.model.vo.CaptionUserVO;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.Sets;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class AnalysisTaskJob extends JavaProcessor {

    /**
     * 最大队列容量
     */
    private static final int MAX_QUEUE_SIZE = 20;

    /**
     * 共享队列，存储待处理的image_ids
     */
    private static final ConcurrentLinkedQueue<String> SHARED_QUEUE = new ConcurrentLinkedQueue<>();

    /**
     * 队列大小计数器
     */
    private static final AtomicInteger QUEUE_SIZE = new AtomicInteger(0);

    /**
     * 幂等性缓存，防止重复添加相同的image_id
     * 缓存过期时间为24小时
     */
    private static final Cache<String, Boolean> PROCESSED_CACHE = CacheBuilder.newBuilder()
            .maximumSize(10000)
            .expireAfterWrite(24, TimeUnit.HOURS)
            .build();

    @Autowired
    private ImageGroupCaptionUserService imageGroupCaptionUserService;

    @Autowired
    private ImageGroupDAO imageGroupDAO;

    @Autowired
    private CaptionUserService captionUserService;

    @Override
    public ProcessResult process(JobContext context) throws Exception {
        String uuid = CommonUtil.uuid();
        MDC.put("traceId", uuid);
        MDC.put("env", StringUtils.upperCase(EnvUtil.getEnv()));
        OperationContextHolder.setContext(CommonUtil.mockSystemContext());
        Params params = Optional.ofNullable(context.getInstanceParameters())
            .filter(StringUtils::isNotBlank)
            .map(s -> JSONObject.parseObject(s, Params.class))
            .orElseGet(() -> Optional.ofNullable(context.getJobParameters())
                .filter(StringUtils::isNotBlank)
                .map(s -> JSONObject.parseObject(s, Params.class))
                .orElse(new Params()));

        log.info("[AnalysisTaskJob] start, params: {}, jobId: {}", params, context.getJobId());

        CaptionUserVO user = captionUserService.getOrCreateByUsername("gemini-flash");

        try {
            // 获取所有未标注的图像ID
            List<String> imageIds = imageGroupDAO.selectAllImageIdsNotCaptionByUser(user.getId());
            log.info("[AnalysisTaskJob] Found {} images not captioned by user {}", imageIds.size(), user.getId());

            // 添加到共享队列（带幂等性检查）
            int addedCount = addToSharedQueue(imageIds, context.getJobId());
            log.info("[AnalysisTaskJob] Added {} images to shared queue, current queue size: {}",
                    addedCount, QUEUE_SIZE.get());

            // 处理队列中的图像（最多处理20个）
            int processedCount = processQueuedImages(user, context.getJobId());
            log.info("[AnalysisTaskJob] Processed {} images from queue", processedCount);

        } finally {
            MDC.remove("env");
            MDC.remove("traceId");
            OperationContextHolder.clean();
        }

        return new ProcessResult(true);
    }

    /**
     * 添加图像ID到共享队列（带幂等性检查）
     *
     * @param imageIds 图像ID列表
     * @param jobId 任务ID
     * @return 实际添加的数量
     */
    private int addToSharedQueue(List<String> imageIds, Long jobId) {
        if (imageIds == null || imageIds.isEmpty()) {
            return 0;
        }

        int addedCount = 0;
        Set<String> alreadyProcessed = Sets.newHashSet();

        // 批量检查幂等性
        for (String imageId : imageIds) {
            if (PROCESSED_CACHE.getIfPresent(imageId) != null) {
                alreadyProcessed.add(imageId);
            }
        }

        log.info("[AnalysisTaskJob] Idempotency check: {} images already processed, {} new images",
                alreadyProcessed.size(), imageIds.size() - alreadyProcessed.size());

        // 添加新的图像ID到队列
        for (String imageId : imageIds) {
            // 检查队列是否已满
            if (QUEUE_SIZE.get() >= MAX_QUEUE_SIZE) {
                log.warn("[AnalysisTaskJob] Queue is full ({}), cannot add more images", MAX_QUEUE_SIZE);
                break;
            }

            // 幂等性检查
            if (alreadyProcessed.contains(imageId)) {
                continue;
            }

            // 添加到队列
            SHARED_QUEUE.offer(imageId);
            QUEUE_SIZE.incrementAndGet();

            // 标记为已处理（防止重复添加）
            PROCESSED_CACHE.put(imageId, true);
            addedCount++;

            log.debug("[AnalysisTaskJob] Added image {} to queue, jobId: {}", imageId, jobId);
        }

        return addedCount;
    }

    /**
     * 处理队列中的图像
     *
     * @param user 用户信息
     * @param jobId 任务ID
     * @return 处理的数量
     */
    private int processQueuedImages(CaptionUserVO user, Long jobId) {
        int processedCount = 0;
        int maxProcess = Math.min(MAX_QUEUE_SIZE, QUEUE_SIZE.get());

        log.info("[AnalysisTaskJob] Starting to process up to {} images from queue", maxProcess);

        for (int i = 0; i < maxProcess; i++) {
            String imageId = SHARED_QUEUE.poll();
            if (imageId == null) {
                break; // 队列为空
            }

            QUEUE_SIZE.decrementAndGet();

            try {
                // 这里可以添加实际的图像处理逻辑
                // 例如：调用图像标注服务
                processImage(imageId, user, jobId);
                processedCount++;

                log.debug("[AnalysisTaskJob] Successfully processed image {}", imageId);

            } catch (Exception e) {
                log.error("[AnalysisTaskJob] Failed to process image {}", imageId, e);
                // 处理失败时，可以选择重新加入队列或记录错误
            }
        }

        return processedCount;
    }

    /**
     * 处理单个图像（占位方法，可根据实际需求实现）
     *
     * @param imageId 图像ID
     * @param user 用户信息
     * @param jobId 任务ID
     */
    private void processImage(String imageId, CaptionUserVO user, Long jobId) {
        // TODO: 实现具体的图像处理逻辑
        // 例如：
        // 1. 调用AI服务进行图像分析
        // 2. 保存标注结果
        // 3. 更新数据库状态

        log.info("[AnalysisTaskJob] Processing image {} for user {} in job {}", imageId, user.getId(), jobId);

        // 模拟处理时间
        try {
            Thread.sleep(100);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    /**
     * 获取队列状态信息
     *
     * @return 队列状态
     */
    public static QueueStatus getQueueStatus() {
        return new QueueStatus(
            QUEUE_SIZE.get(),
            MAX_QUEUE_SIZE,
            (int) PROCESSED_CACHE.size()
        );
    }

    /**
     * 队列状态信息
     */
    public static class QueueStatus {
        private final int currentSize;
        private final int maxSize;
        private final int processedCount;

        public QueueStatus(int currentSize, int maxSize, int processedCount) {
            this.currentSize = currentSize;
            this.maxSize = maxSize;
            this.processedCount = processedCount;
        }

        public int getCurrentSize() { return currentSize; }
        public int getMaxSize() { return maxSize; }
        public int getProcessedCount() { return processedCount; }

        @Override
        public String toString() {
            return String.format("QueueStatus{current=%d, max=%d, processed=%d}",
                               currentSize, maxSize, processedCount);
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    private static class Params {
        /**
         * 是否强制更新
         */
        private boolean forceUpdate;

        /**
         * 用户ID列表
         */
        private List<Integer> userIds;
    }
}
