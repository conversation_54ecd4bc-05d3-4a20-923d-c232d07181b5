package ai.conrain.aigc.platform.service.job;

import ai.conrain.aigc.platform.dal.pgsql.dao.ImageGroupDAO;
import ai.conrain.aigc.platform.integration.utils.EnvUtil;
import ai.conrain.aigc.platform.service.component.CaptionUserService;
import ai.conrain.aigc.platform.service.component.ImageGroupCaptionUserService;
import ai.conrain.aigc.platform.service.model.vo.CaptionUserVO;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import java.util.List;
import java.util.Optional;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class AnalysisTaskJob extends JavaProcessor {

    @Autowired
    private ImageGroupCaptionUserService imageGroupCaptionUserService;

    @Autowired
    private ImageGroupDAO imageGroupDAO;

    @Autowired
    private CaptionUserService captionUserService;

    @Override
    public ProcessResult process(JobContext context) throws Exception {
        String uuid = CommonUtil.uuid();
        MDC.put("traceId", uuid);
        MDC.put("env", StringUtils.upperCase(EnvUtil.getEnv()));
        OperationContextHolder.setContext(CommonUtil.mockSystemContext());
        Params params = Optional.ofNullable(context.getInstanceParameters())
            .filter(StringUtils::isNotBlank)
            .map(s -> JSONObject.parseObject(s, Params.class))
            .orElseGet(() -> Optional.ofNullable(context.getJobParameters())
                .filter(StringUtils::isNotBlank)
                .map(s -> JSONObject.parseObject(s, Params.class))
                .orElse(new Params()));

        log.info("[AnalysisTaskJob] start, params: {}", params);

        CaptionUserVO user = captionUserService.getOrCreateByUsername("gemini-flash");

        try {
            List<String> imageIds = imageGroupDAO.selectAllImageIdsNotCaptionByUser(user.getId());

        } finally {
            MDC.remove("env");
            MDC.remove("traceId");
            OperationContextHolder.clean();
        }

        return new ProcessResult(true);
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    private static class Params {
        /**
         * 是否强制更新
         */
        private boolean forceUpdate;

        /**
         * 用户ID列表
         */
        private List<Integer> userIds;
    }
}
