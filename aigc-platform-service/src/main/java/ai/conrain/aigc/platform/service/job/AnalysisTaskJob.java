package ai.conrain.aigc.platform.service.job;

import ai.conrain.aigc.platform.dal.pgsql.dao.ImageGroupDAO;
import ai.conrain.aigc.platform.integration.utils.EnvUtil;
import ai.conrain.aigc.platform.service.component.CaptionUserService;
import ai.conrain.aigc.platform.service.component.ImageGroupCaptionUserService;
import ai.conrain.aigc.platform.service.model.vo.CaptionUserVO;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.Sets;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class AnalysisTaskJob extends JavaProcessor {

    /**
     * 固定大小的虚拟线程池，最多20个并发任务
     */
    private static final ExecutorService VIRTUAL_THREAD_POOL = Executors.newFixedThreadPool(20,
            Thread.ofVirtual().name("image-processing-", 0).factory());

    /**
     * 无界阻塞队列，存储待处理的image_ids
     */
    private static final BlockingQueue<String> TASK_QUEUE = new LinkedBlockingQueue<>();

    /**
     * 幂等性缓存，防止重复添加相同的image_id
     * 缓存过期时间为24小时
     */
    private static final Cache<String, Boolean> PROCESSED_CACHE = CacheBuilder.newBuilder()
            .maximumSize(10000)
            .expireAfterWrite(24, TimeUnit.HOURS)
            .build();

    @Autowired
    private ImageGroupCaptionUserService imageGroupCaptionUserService;

    @Autowired
    private ImageGroupDAO imageGroupDAO;

    @Autowired
    private CaptionUserService captionUserService;

    @Override
    public ProcessResult process(JobContext context) throws Exception {
        String uuid = CommonUtil.uuid();
        MDC.put("traceId", uuid);
        MDC.put("env", StringUtils.upperCase(EnvUtil.getEnv()));
        OperationContextHolder.setContext(CommonUtil.mockSystemContext());
        Params params = Optional.ofNullable(context.getInstanceParameters())
            .filter(StringUtils::isNotBlank)
            .map(s -> JSONObject.parseObject(s, Params.class))
            .orElseGet(() -> Optional.ofNullable(context.getJobParameters())
                .filter(StringUtils::isNotBlank)
                .map(s -> JSONObject.parseObject(s, Params.class))
                .orElse(new Params()));

        log.info("[AnalysisTaskJob] start, params: {}, jobId: {}", params, context.getJobId());

        CaptionUserVO user = captionUserService.getOrCreateByUsername("gemini-flash");

        try {
            // 获取所有未标注的图像ID
            List<String> imageIds = imageGroupDAO.selectAllImageIdsNotCaptionByUser(user.getId());
            log.info("[AnalysisTaskJob] Found {} images not captioned by user {}", imageIds.size(), user.getId());

            // 添加到任务队列（带幂等性检查）
            int addedCount = addToTaskQueue(imageIds, context.getJobId());
            log.info("[AnalysisTaskJob] Added {} images to task queue, current queue size: {}",
                    addedCount, TASK_QUEUE.size());

            // 提交任务到虚拟线程池处理
            submitTasksToThreadPool(user, context.getJobId());
            log.info("[AnalysisTaskJob] Submitted tasks to virtual thread pool for processing");

        } finally {
            MDC.remove("env");
            MDC.remove("traceId");
            OperationContextHolder.clean();
        }

        return new ProcessResult(true);
    }

    /**
     * 添加图像ID到任务队列（带幂等性检查）
     *
     * @param imageIds 图像ID列表
     * @param jobId 任务ID
     * @return 实际添加的数量
     */
    private int addToTaskQueue(List<String> imageIds, Long jobId) {
        if (imageIds == null || imageIds.isEmpty()) {
            return 0;
        }

        int addedCount = 0;
        Set<String> alreadyProcessed = Sets.newHashSet();

        // 批量检查幂等性
        for (String imageId : imageIds) {
            if (PROCESSED_CACHE.getIfPresent(imageId) != null) {
                alreadyProcessed.add(imageId);
            }
        }

        log.info("[AnalysisTaskJob] Idempotency check: {} images already processed, {} new images",
                alreadyProcessed.size(), imageIds.size() - alreadyProcessed.size());

        // 添加新的图像ID到队列
        for (String imageId : imageIds) {
            // 幂等性检查
            if (alreadyProcessed.contains(imageId)) {
                continue;
            }

            try {
                // 添加到无界队列
                TASK_QUEUE.put(imageId);

                // 标记为已处理（防止重复添加）
                PROCESSED_CACHE.put(imageId, true);
                addedCount++;

                log.debug("[AnalysisTaskJob] Added image {} to task queue, jobId: {}", imageId, jobId);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("[AnalysisTaskJob] Interrupted while adding image {} to queue", imageId, e);
                break;
            }
        }

        return addedCount;
    }

    /**
     * 提交任务到虚拟线程池处理
     *
     * @param user 用户信息
     * @param jobId 任务ID
     */
    private void submitTasksToThreadPool(CaptionUserVO user, Long jobId) {
        int queueSize = TASK_QUEUE.size();
        log.info("[AnalysisTaskJob] Starting to submit {} tasks to virtual thread pool", queueSize);

        // 提交所有队列中的任务到线程池
        // 线程池会自动管理并发数量（最多20个并发）
        while (!TASK_QUEUE.isEmpty()) {
            try {
                String imageId = TASK_QUEUE.take(); // 阻塞获取

                // 提交任务到虚拟线程池
                VIRTUAL_THREAD_POOL.submit(() -> {
                    try {
                        processImage(imageId, user, jobId);
                        log.debug("[AnalysisTaskJob] Successfully processed image {} in virtual thread", imageId);
                    } catch (Exception e) {
                        log.error("[AnalysisTaskJob] Failed to process image {} in virtual thread", imageId, e);
                    }
                });

            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("[AnalysisTaskJob] Interrupted while taking task from queue", e);
                break;
            }
        }

        log.info("[AnalysisTaskJob] All tasks submitted to virtual thread pool");
    }

    /**
     * 处理单个图像（占位方法，可根据实际需求实现）
     *
     * @param imageId 图像ID
     * @param user 用户信息
     * @param jobId 任务ID
     */
    private void processImage(String imageId, CaptionUserVO user, Long jobId) {
        // TODO: 实现具体的图像处理逻辑
        // 例如：
        // 1. 调用AI服务进行图像分析
        // 2. 保存标注结果
        // 3. 更新数据库状态

        log.info("[AnalysisTaskJob] Processing image {} for user {} in job {}", imageId, user.getId(), jobId);

        // 模拟处理时间
        try {
            Thread.sleep(100);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    /**
     * 获取队列和线程池状态信息
     *
     * @return 状态信息
     */
    public static TaskStatus getTaskStatus() {
        return new TaskStatus(
            TASK_QUEUE.size(),
            (int) PROCESSED_CACHE.size(),
            !VIRTUAL_THREAD_POOL.isShutdown()
        );
    }

    /**
     * 关闭线程池（应用关闭时调用）
     */
    public static void shutdown() {
        VIRTUAL_THREAD_POOL.shutdown();
        try {
            if (!VIRTUAL_THREAD_POOL.awaitTermination(30, TimeUnit.SECONDS)) {
                VIRTUAL_THREAD_POOL.shutdownNow();
            }
        } catch (InterruptedException e) {
            VIRTUAL_THREAD_POOL.shutdownNow();
            Thread.currentThread().interrupt();
        }
        log.info("[AnalysisTaskJob] Virtual thread pool shutdown completed");
    }

    /**
     * 任务状态信息
     */
    public static class TaskStatus {
        private final int queueSize;
        private final int processedCount;
        private final boolean threadPoolActive;

        public TaskStatus(int queueSize, int processedCount, boolean threadPoolActive) {
            this.queueSize = queueSize;
            this.processedCount = processedCount;
            this.threadPoolActive = threadPoolActive;
        }

        public int getQueueSize() { return queueSize; }
        public int getProcessedCount() { return processedCount; }
        public boolean isThreadPoolActive() { return threadPoolActive; }

        @Override
        public String toString() {
            return String.format("TaskStatus{queue=%d, processed=%d, threadPoolActive=%s}",
                               queueSize, processedCount, threadPoolActive);
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    private static class Params {
        /**
         * 是否强制更新
         */
        private boolean forceUpdate;

        /**
         * 用户ID列表
         */
        private List<Integer> userIds;
    }
}
