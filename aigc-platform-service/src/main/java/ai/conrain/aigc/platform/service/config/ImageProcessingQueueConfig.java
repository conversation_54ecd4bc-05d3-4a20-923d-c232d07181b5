package ai.conrain.aigc.platform.service.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 图像处理队列配置
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "aigc.image-processing.queue")
public class ImageProcessingQueueConfig {
    
    /**
     * 最大队列大小
     */
    private int maxSize = 20;
    
    /**
     * 幂等性缓存最大大小
     */
    private int idempotentCacheMaxSize = 10000;
    
    /**
     * 幂等性缓存过期时间（小时）
     */
    private int idempotentCacheExpireHours = 24;
    
    /**
     * 是否启用队列功能
     */
    private boolean enabled = true;
    
    /**
     * 处理超时时间（毫秒）
     */
    private long processTimeoutMs = 30000;
    
    /**
     * 批处理大小
     */
    private int batchSize = 10;
    
    /**
     * 是否启用详细日志
     */
    private boolean verboseLogging = false;
}
