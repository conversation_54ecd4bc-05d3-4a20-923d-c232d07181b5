package ai.conrain.aigc.platform.service.util;

import ai.conrain.aigc.platform.service.job.AnalysisTaskJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 队列监控工具类
 */
@Slf4j
@Component
public class QueueMonitorUtil {
    
    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    /**
     * 打印任务状态
     */
    public static void printTaskStatus() {
        AnalysisTaskJob.TaskStatus status = AnalysisTaskJob.getTaskStatus();
        String timestamp = LocalDateTime.now().format(FORMATTER);

        log.info("=== Task Status at {} ===", timestamp);
        log.info("Queue Size: {}", status.getQueueSize());
        log.info("Processed Count: {}", status.getProcessedCount());
        log.info("Thread Pool Active: {}", status.isThreadPoolActive());
        log.info("=====================================");
    }
    
    /**
     * 检查任务系统是否健康
     *
     * @return true如果任务系统状态正常
     */
    public static boolean isTaskSystemHealthy() {
        AnalysisTaskJob.TaskStatus status = AnalysisTaskJob.getTaskStatus();

        // 检查线程池是否活跃
        if (!status.isThreadPoolActive()) {
            log.warn("Virtual thread pool is not active");
            return false;
        }

        // 检查队列大小是否过大（可配置阈值）
        if (status.getQueueSize() > 1000) {
            log.warn("Task queue size is too large: {}", status.getQueueSize());
            return false;
        }

        return true;
    }

    /**
     * 获取任务状态摘要
     *
     * @return 状态摘要字符串
     */
    public static String getTaskSummary() {
        AnalysisTaskJob.TaskStatus status = AnalysisTaskJob.getTaskStatus();
        return String.format("Tasks: queue=%d, processed=%d, threadPool=%s",
                           status.getQueueSize(),
                           status.getProcessedCount(),
                           status.isThreadPoolActive() ? "active" : "inactive");
    }

    /**
     * 检查是否需要告警
     *
     * @return true如果需要告警
     */
    public static boolean shouldAlert() {
        AnalysisTaskJob.TaskStatus status = AnalysisTaskJob.getTaskStatus();

        // 线程池不活跃时告警
        if (!status.isThreadPoolActive()) {
            return true;
        }

        // 队列积压过多时告警
        return status.getQueueSize() > 1000;
    }

    /**
     * 记录任务指标（用于监控系统）
     */
    public static void recordMetrics() {
        AnalysisTaskJob.TaskStatus status = AnalysisTaskJob.getTaskStatus();

        // 这里可以集成到监控系统，如Prometheus、Micrometer等
        log.debug("Task metrics - queue: {}, processed: {}, threadPoolActive: {}",
                 status.getQueueSize(),
                 status.getProcessedCount(),
                 status.isThreadPoolActive());
    }
}
