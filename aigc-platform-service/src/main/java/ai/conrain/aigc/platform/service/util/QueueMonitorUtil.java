package ai.conrain.aigc.platform.service.util;

import ai.conrain.aigc.platform.service.job.AnalysisTaskJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 队列监控工具类
 */
@Slf4j
@Component
public class QueueMonitorUtil {
    
    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    /**
     * 打印队列状态
     */
    public static void printQueueStatus() {
        AnalysisTaskJob.QueueStatus status = AnalysisTaskJob.getQueueStatus();
        String timestamp = LocalDateTime.now().format(FORMATTER);
        
        log.info("=== Queue Status at {} ===", timestamp);
        log.info("Current Size: {}", status.getCurrentSize());
        log.info("Max Size: {}", status.getMaxSize());
        log.info("Processed Count: {}", status.getProcessedCount());
        log.info("Queue Usage: {:.1f}%", (double) status.getCurrentSize() / status.getMaxSize() * 100);
        log.info("=====================================");
    }
    
    /**
     * 检查队列是否健康
     * 
     * @return true如果队列状态正常
     */
    public static boolean isQueueHealthy() {
        AnalysisTaskJob.QueueStatus status = AnalysisTaskJob.getQueueStatus();
        
        // 检查队列是否过满
        double usage = (double) status.getCurrentSize() / status.getMaxSize();
        if (usage > 0.9) {
            log.warn("Queue usage is high: {:.1f}%", usage * 100);
            return false;
        }
        
        return true;
    }
    
    /**
     * 获取队列使用率
     * 
     * @return 使用率百分比
     */
    public static double getQueueUsagePercentage() {
        AnalysisTaskJob.QueueStatus status = AnalysisTaskJob.getQueueStatus();
        return (double) status.getCurrentSize() / status.getMaxSize() * 100;
    }
    
    /**
     * 获取队列状态摘要
     * 
     * @return 状态摘要字符串
     */
    public static String getQueueSummary() {
        AnalysisTaskJob.QueueStatus status = AnalysisTaskJob.getQueueStatus();
        return String.format("Queue: %d/%d (%.1f%%), Processed: %d", 
                           status.getCurrentSize(), 
                           status.getMaxSize(),
                           getQueueUsagePercentage(),
                           status.getProcessedCount());
    }
    
    /**
     * 检查是否需要告警
     * 
     * @return true如果需要告警
     */
    public static boolean shouldAlert() {
        AnalysisTaskJob.QueueStatus status = AnalysisTaskJob.getQueueStatus();
        
        // 队列使用率超过90%时告警
        double usage = (double) status.getCurrentSize() / status.getMaxSize();
        return usage > 0.9;
    }
    
    /**
     * 记录队列指标（用于监控系统）
     */
    public static void recordMetrics() {
        AnalysisTaskJob.QueueStatus status = AnalysisTaskJob.getQueueStatus();
        
        // 这里可以集成到监控系统，如Prometheus、Micrometer等
        log.debug("Queue metrics - current: {}, max: {}, processed: {}, usage: {:.1f}%",
                 status.getCurrentSize(),
                 status.getMaxSize(),
                 status.getProcessedCount(),
                 getQueueUsagePercentage());
    }
}
