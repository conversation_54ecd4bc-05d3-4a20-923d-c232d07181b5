package ai.conrain.aigc.platform.service.component.event;

import lombok.Getter;
import org.springframework.context.ApplicationEvent;

import java.time.LocalDateTime;

/**
 * 图像处理事件基类
 */
@Getter
public abstract class ImageProcessingEvent extends ApplicationEvent {
    
    private final String imageId;
    private final String jobId;
    private final LocalDateTime timestamp;
    
    public ImageProcessingEvent(Object source, String imageId, String jobId) {
        super(source);
        this.imageId = imageId;
        this.jobId = jobId;
        this.timestamp = LocalDateTime.now();
    }
    
    /**
     * 图像添加到队列事件
     */
    public static class ImageAddedToQueueEvent extends ImageProcessingEvent {
        private final int queueSize;
        
        public ImageAddedToQueueEvent(Object source, String imageId, String jobId, int queueSize) {
            super(source, imageId, jobId);
            this.queueSize = queueSize;
        }
        
        public int getQueueSize() {
            return queueSize;
        }
    }
    
    /**
     * 图像开始处理事件
     */
    public static class ImageProcessingStartedEvent extends ImageProcessingEvent {
        public ImageProcessingStartedEvent(Object source, String imageId, String jobId) {
            super(source, imageId, jobId);
        }
    }
    
    /**
     * 图像处理完成事件
     */
    public static class ImageProcessingCompletedEvent extends ImageProcessingEvent {
        private final boolean success;
        private final String errorMessage;
        
        public ImageProcessingCompletedEvent(Object source, String imageId, String jobId, boolean success, String errorMessage) {
            super(source, imageId, jobId);
            this.success = success;
            this.errorMessage = errorMessage;
        }
        
        public boolean isSuccess() {
            return success;
        }
        
        public String getErrorMessage() {
            return errorMessage;
        }
    }
    
    /**
     * 队列状态变化事件
     */
    public static class QueueStatusChangedEvent extends ApplicationEvent {
        private final int currentSize;
        private final int maxSize;
        private final int processedCount;
        private final int processingCount;
        private final LocalDateTime timestamp;
        
        public QueueStatusChangedEvent(Object source, int currentSize, int maxSize, int processedCount, int processingCount) {
            super(source);
            this.currentSize = currentSize;
            this.maxSize = maxSize;
            this.processedCount = processedCount;
            this.processingCount = processingCount;
            this.timestamp = LocalDateTime.now();
        }
        
        public int getCurrentSize() { return currentSize; }
        public int getMaxSize() { return maxSize; }
        public int getProcessedCount() { return processedCount; }
        public int getProcessingCount() { return processingCount; }
        public LocalDateTime getTimestamp() { return timestamp; }
    }
    
    /**
     * 批量图像添加事件
     */
    public static class BatchImagesAddedEvent extends ApplicationEvent {
        private final int addedCount;
        private final int skippedCount;
        private final String jobId;
        private final LocalDateTime timestamp;
        
        public BatchImagesAddedEvent(Object source, int addedCount, int skippedCount, String jobId) {
            super(source);
            this.addedCount = addedCount;
            this.skippedCount = skippedCount;
            this.jobId = jobId;
            this.timestamp = LocalDateTime.now();
        }
        
        public int getAddedCount() { return addedCount; }
        public int getSkippedCount() { return skippedCount; }
        public String getJobId() { return jobId; }
        public LocalDateTime getTimestamp() { return timestamp; }
    }
}
