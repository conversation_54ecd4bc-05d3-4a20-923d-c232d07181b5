package ai.conrain.aigc.platform.service.component.event;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

/**
 * 图像处理事件发布器
 */
@Slf4j
@Component
public class ImageProcessingEventPublisher {
    
    @Autowired
    private ApplicationEventPublisher eventPublisher;
    
    /**
     * 发布图像添加到队列事件
     */
    public void publishImageAddedToQueue(String imageId, String jobId, int queueSize) {
        try {
            ImageProcessingEvent.ImageAddedToQueueEvent event = 
                new ImageProcessingEvent.ImageAddedToQueueEvent(this, imageId, jobId, queueSize);
            eventPublisher.publishEvent(event);
            log.debug("[ImageProcessingEventPublisher] Published ImageAddedToQueueEvent for image: {}", imageId);
        } catch (Exception e) {
            log.error("[ImageProcessingEventPublisher] Failed to publish ImageAddedToQueueEvent", e);
        }
    }
    
    /**
     * 发布图像开始处理事件
     */
    public void publishImageProcessingStarted(String imageId, String jobId) {
        try {
            ImageProcessingEvent.ImageProcessingStartedEvent event = 
                new ImageProcessingEvent.ImageProcessingStartedEvent(this, imageId, jobId);
            eventPublisher.publishEvent(event);
            log.debug("[ImageProcessingEventPublisher] Published ImageProcessingStartedEvent for image: {}", imageId);
        } catch (Exception e) {
            log.error("[ImageProcessingEventPublisher] Failed to publish ImageProcessingStartedEvent", e);
        }
    }
    
    /**
     * 发布图像处理完成事件
     */
    public void publishImageProcessingCompleted(String imageId, String jobId, boolean success, String errorMessage) {
        try {
            ImageProcessingEvent.ImageProcessingCompletedEvent event = 
                new ImageProcessingEvent.ImageProcessingCompletedEvent(this, imageId, jobId, success, errorMessage);
            eventPublisher.publishEvent(event);
            log.debug("[ImageProcessingEventPublisher] Published ImageProcessingCompletedEvent for image: {}, success: {}", 
                     imageId, success);
        } catch (Exception e) {
            log.error("[ImageProcessingEventPublisher] Failed to publish ImageProcessingCompletedEvent", e);
        }
    }
    
    /**
     * 发布队列状态变化事件
     */
    public void publishQueueStatusChanged(int currentSize, int maxSize, int processedCount, int processingCount) {
        try {
            ImageProcessingEvent.QueueStatusChangedEvent event = 
                new ImageProcessingEvent.QueueStatusChangedEvent(this, currentSize, maxSize, processedCount, processingCount);
            eventPublisher.publishEvent(event);
            log.debug("[ImageProcessingEventPublisher] Published QueueStatusChangedEvent: current={}, max={}, processed={}, processing={}", 
                     currentSize, maxSize, processedCount, processingCount);
        } catch (Exception e) {
            log.error("[ImageProcessingEventPublisher] Failed to publish QueueStatusChangedEvent", e);
        }
    }
    
    /**
     * 发布批量图像添加事件
     */
    public void publishBatchImagesAdded(int addedCount, int skippedCount, String jobId) {
        try {
            ImageProcessingEvent.BatchImagesAddedEvent event = 
                new ImageProcessingEvent.BatchImagesAddedEvent(this, addedCount, skippedCount, jobId);
            eventPublisher.publishEvent(event);
            log.info("[ImageProcessingEventPublisher] Published BatchImagesAddedEvent: added={}, skipped={}, jobId={}", 
                    addedCount, skippedCount, jobId);
        } catch (Exception e) {
            log.error("[ImageProcessingEventPublisher] Failed to publish BatchImagesAddedEvent", e);
        }
    }
}
