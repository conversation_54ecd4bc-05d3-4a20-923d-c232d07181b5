package ai.conrain.aigc.platform.service.component.creative;

import ai.conrain.aigc.platform.integration.ai.ComfyUIServiceImpl;
import ai.conrain.aigc.platform.integration.gpt.AIService;
import ai.conrain.aigc.platform.integration.taobao.huiwa.HuiWaService;
import ai.conrain.aigc.platform.service.component.CommonTaskService;
import ai.conrain.aigc.platform.service.component.SystemConfigService;
import ai.conrain.aigc.platform.service.component.dispatch.ServerHelper;
import ai.conrain.aigc.platform.service.constants.CommonConstants;
import ai.conrain.aigc.platform.service.constants.SystemConstants;
import ai.conrain.aigc.platform.service.enums.*;
import ai.conrain.aigc.platform.service.helper.ComfyUIHelper;
import ai.conrain.aigc.platform.service.model.biz.ClothColorDetail;
import ai.conrain.aigc.platform.service.model.biz.ClothTypeConfig;
import ai.conrain.aigc.platform.service.model.biz.LoraTrainDetail;
import ai.conrain.aigc.platform.service.model.common.BizException;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.converter.CreativeTaskConverter;
import ai.conrain.aigc.platform.service.model.request.AddCreativeRequest;
import ai.conrain.aigc.platform.service.model.vo.*;
import ai.conrain.aigc.platform.service.util.ComfyUIUtils;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.service.util.ElementUtils;
import ai.conrain.aigc.platform.service.util.FlowUtils;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import static ai.conrain.aigc.platform.service.constants.CommonConstants.*;


@Slf4j
@Service
public class BasicChangingClothesService extends CreateImageCreativeService {

    @Autowired
    private ComfyUIHelper comfyUIHelper;
    @Autowired
    private ComfyUIServiceImpl comfyUIServiceImpl;
    @Autowired
    private ServerHelper serverHelper;
    @Value("${comfyui.output.path}")
    private String comfyuiOutputPath;
    @Autowired
    private HuiWaService huiWaService;
    @Autowired
    private CommonTaskService commonTaskService;
    @Autowired
    private FaceSceneSwitchCreativeService faceSceneSwitchCreativeService;
    @Autowired
    private ObjectMapper objectMapper;
    @Autowired
    private AIService aiService;
    @Autowired
    private SystemConfigService systemConfigService;

    @Override
    public CreativeTypeEnum getCreativeType() {
        return CreativeTypeEnum.BASIC_CHANGING_CLOTHES;
    }

    @Override
    protected MaterialModelVO fetchModel(AddCreativeRequest request) {
        return mockModelVO();
    }

    @Override
    protected CreativeBatchVO buildData(AddCreativeRequest request, MaterialModelVO modelVO) {
        log.info(
                "[ComfyUI流程][基础款换衣]BasicChangingClothesCreativeService::buildData::开始构建CreativeBatchVO 批次数据...");

        CreativeBatchVO batch = super.buildData(request, modelVO);

        // 如果配置为空则说明是用户上传的参考图，分辨率设置为空
        if (request.getReferenceInfoList().get(0).getReferenceConfig() == null) {
            batch.setImageProportion("NONE");
        }

        //  设置任务类型
        batch.setType(getCreativeType());
        // 设置任务数量（每一张参考图对应两个task）
        batch.setBatchCnt(request.getReferenceInfoList().size());
        // 添加 衣服类型
        batch.addExtInfo(CLOTHE_TYPE, request.getClotheType());
        // 添加服装图片
        batch.addExtInfo(SHOW_CLOTHES_PIC_URL, request.getClotheImage());
        // 额外设置一份
        batch.addExtInfo(SHOW_CLOTHES_PIC, request.getClotheImage());
        // 添加 mask 图片
        batch.addExtInfo(MASK_IMAGE_URL, request.getMaskImage());

        // 添加抠图 prompt
        batch.addExtInfo(PICTURE_MATTING_PROMPT, getPictureMattingPrompt(request));

        // 添加是否需要替换人脸
        batch.addExtInfo(IS_NEED_REPLACE_FACE,
                request.getConfigs() != null && !request.getConfigs().isEmpty());

        // ReferenceInfoList添加进入扩展信息中
        batch.addExtInfo(REFERENCE_INFO_LIST, request.getReferenceInfoList());

        AddCreativeRequest.ReferenceInfo referenceInfo = request.getReferenceInfoList().get(0);
        // 是否是系统上传
        boolean isSystemUpload = Objects.nonNull(referenceInfo.getReferenceConfig());

        // 是否是用户上传参考图
        batch.addExtInfo(IS_USER_UPLOAD_REFERENCE, !isSystemUpload);

        List<String> list = new ArrayList<>(Collections.singletonList(CameraAngleEnum.FRONT_VIEW.getCode()));
        if (request.getClotheType().contains("upper")) {
            list.add(CameraAngleEnum.UPPER_BODY.getCode());
        } else if (request.getClotheType().contains("lower")) {
            list.add(CameraAngleEnum.LOWER_BODY.getCode());
        }
        // 添加视角信息
        batch.addExtInfo(KEY_CAMERA_ANGLE, list);

        // 目前固定姿势创建出图为 1+1+1(tryOn一张、绘蛙均衡一张、绘蛙质量一张)，固最终需要三个任务的结果
        batch.addExtInfo(KEY_IS_MULTI_PROCESS, YES);
        batch.addExtInfo(FINISH_TASK_COUNT, 3);


        log.info(
                "[ComfyUI流程][基础款换衣]BasicChangingClothesCreativeService::buildData::CreativeBatchVO 批次数据构建完成:{}",
                batch);
        // 返回结果
        return batch;
    }

    @Override
    public List<CreativeTaskVO> buildTasks(CreativeBatchVO batch, List<CreativeElementVO> elements) {
        log.info("[ComfyUI流程][基础款换衣]BasicChangingClothesCreativeService::buildTasks::开始构建CreativeTaskVO 任务数据...");

        List<AddCreativeRequest.ReferenceInfo> referenceInfoList = objectMapper.convertValue(
                batch.getExtInfo().get(REFERENCE_INFO_LIST), new TypeReference<List<AddCreativeRequest.ReferenceInfo>>() {
                });

        if (CollectionUtils.isEmpty(referenceInfoList)) {
            return Collections.emptyList();
        }

        // 1. 并行上传图片
        UploadedImageUrls uploadedImageUrls = uploadImages(batch, referenceInfoList);

        // 2. Batch fetch scene elements
        Map<Integer, CreativeElementVO> sceneElementMap = batchFetchSceneElements(referenceInfoList);

        // 3. 构建 Task 任务
        List<CreativeTaskVO> result = new ArrayList<>();
        for (int i = 0; i < referenceInfoList.size(); i++) {
            // 获取当前的参考图信息
            AddCreativeRequest.ReferenceInfo referenceInfo = referenceInfoList.get(i);

            // 构建 Task
            List<CreativeTaskVO> tasksForReference = createTasksForReference(batch, referenceInfo, elements,
                    sceneElementMap, uploadedImageUrls, i);

            // 添加进入结果中
            result.addAll(tasksForReference);
        }

        log.info("[ComfyUI流程][基础款换衣]BasicChangingClothesCreativeService::buildTasks::CreativeTaskVO 任务数据构建完成,共：{}个任务",
                result.size());
        return result;
    }


    /**
     * 并行上传图片信息
     *
     * @param batch             批次信息
     * @param referenceInfoList 参考图列表
     * @return
     */
    private UploadedImageUrls uploadImages(CreativeBatchVO batch,
                                           List<AddCreativeRequest.ReferenceInfo> referenceInfoList) {
        List<CompletableFuture<?>> allUploadFutures = new ArrayList<>();

        CompletableFuture<String> clotheImageFuture = CompletableFuture.supplyAsync(() -> {
            try {
                return comfyUIHelper.upLoadImage(batch.getExtInfo(SHOW_CLOTHES_PIC_URL));
            } catch (IOException e) {
                log.error("[ComfyUI流程][基础款换衣]服装图片上传失败: {}", e.getMessage());
                throw new RuntimeException("服装图片上传失败", e);
            }
        });
        allUploadFutures.add(clotheImageFuture);

        CompletableFuture<String> maskImageFuture = CompletableFuture.supplyAsync(() -> {
            try {
                return comfyUIHelper.upLoadImage(batch.getExtInfo(MASK_IMAGE_URL));
            } catch (IOException e) {
                log.error("[ComfyUI流程][基础款换衣]蒙层图片上传失败: {}", e.getMessage());
                throw new RuntimeException("蒙层图片上传失败", e);
            }
        });
        allUploadFutures.add(maskImageFuture);

        referenceInfoList.forEach(referenceInfo -> {
            CompletableFuture<Void> referenceImageFuture = CompletableFuture.supplyAsync(() -> {
                try {
                    return comfyUIHelper.upLoadImage(referenceInfo.getImageUrl());
                } catch (IOException e) {
                    log.error("[ComfyUI流程][基础款换衣]参考图片上传失败: {}", e.getMessage());
                    throw new RuntimeException("参考图片上传失败", e);
                }
            }).thenAccept(referenceInfo::setUploadComfyUIImageUrl);
            allUploadFutures.add(referenceImageFuture);
        });

        try {
            CompletableFuture.allOf(allUploadFutures.toArray(new CompletableFuture[0])).join();
            String uploadClotheImage = clotheImageFuture.get();
            String uploadMaskImage = maskImageFuture.get();
            return new UploadedImageUrls(uploadClotheImage, uploadMaskImage);
        } catch (Exception e) {
            log.error("[ComfyUI流程][基础款换衣]图片上传失败，终止流程: {}", e.getMessage());
            throw new BizException(ResultCode.BIZ_FAIL, "图片上传失败，请稍后重试");
        }
    }

    /**
     * 批量拉取场景/模特数据
     *
     * @param referenceInfoList 参考图列表
     * @return Map 集合
     */
    private Map<Integer, CreativeElementVO> batchFetchSceneElements(
            List<AddCreativeRequest.ReferenceInfo> referenceInfoList) {
        // 提取 Id 集合
        Set<Integer> loraIds = referenceInfoList.stream()
                .map(AddCreativeRequest.ReferenceInfo::getLoraId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        if (loraIds.isEmpty()) {
            return Collections.emptyMap();
        }

        // 批量查询
        List<CreativeElementVO> sceneElements = creativeElementService.batchQueryByIds(new ArrayList<>(loraIds));
        return sceneElements.stream()
                .collect(Collectors.toMap(CreativeElementVO::getId, element -> element));
    }

    /**
     * 构建任务列表
     *
     * @param batch             批次信息
     * @param referenceInfo     参考图信息
     * @param elements          场景模特信息
     * @param sceneElementMap   map 集合
     * @param uploadedImageUrls 上传图片 URL
     * @param index             索引
     * @return 创建完成的任务
     */
    private List<CreativeTaskVO> createTasksForReference(CreativeBatchVO batch,
                                                         AddCreativeRequest.ReferenceInfo referenceInfo,
                                                         List<CreativeElementVO> elements,
                                                         Map<Integer, CreativeElementVO> sceneElementMap,
                                                         UploadedImageUrls uploadedImageUrls, int index) {
        CreativeTaskVO target = CreativeTaskConverter.request2VO(batch);


        // 1、获取 LoraId
        Integer loraId = referenceInfo.getLoraId();
        if (loraId != null && sceneElementMap.containsKey(loraId)) {
            elements.add(sceneElementMap.get(loraId));
        }
        // 2、填充扩展信息
        fillTaskExt(target, batch, elements, index);
        // 3、填充参考图相关信息
        populateTaskWithReferenceInfo(target, referenceInfo);
        // 4、填充批次关联信息
        populateTaskWithBatchInfo(target, batch);
        // 5、填充上传图片信息
        populateTaskWithUploadedImages(target, batch, referenceInfo, uploadedImageUrls);
        // 6、添加相关任务额外参数
        fillPreTaskExt(target, batch, elements, referenceInfo);
        // 7、当前任务设置为主任务
        target.addExtInfo(KEY_IS_MAIN_TASK, YES);

        CreativeTaskVO data = creativeTaskService.insert(target);

        // 8、构建多阶段任务
        List<CreativeTaskVO> dependentTasks = buildMultiProcessTask(batch, data, elements);

        List<CreativeTaskVO> createdTasks = new ArrayList<>();
        createdTasks.add(data);
        createdTasks.addAll(dependentTasks.stream().filter(Objects::nonNull).toList());

        return createdTasks;
    }

    /**
     * 添加与之相关的任务额外参数
     *
     * @param target        目标任务
     * @param batch         目标批次
     * @param elements      场景/模特
     * @param referenceInfo 参考图信息
     */
    private void fillPreTaskExt(CreativeTaskVO target, CreativeBatchVO batch, List<CreativeElementVO> elements, AddCreativeRequest.ReferenceInfo referenceInfo) {
        // modelId
        target.addExtInfo(KEY_MODEL_ID, referenceInfo.getLoraId());

        // 添加风格lora配置
        if (Objects.nonNull(referenceInfo.getReferenceConfig())) {
            // 设置 lens
            target.addExtInfo(CommonConstants.KEY_LENS,
                    referenceInfo.getReferenceConfig().get(CommonConstants.KEY_LENS));
            // 设置 posture
            target.addExtInfo(CommonConstants.KEY_POSTURE,
                    referenceInfo.getReferenceConfig().get(CommonConstants.KEY_POSTURE));
            // 设置 style
            target.addExtInfo(CommonConstants.KEY_STYLE,
                    referenceInfo.getReferenceConfig().get(CommonConstants.KEY_STYLE));
        }
        // 设置扩展标签
        target.addExtInfo(CommonConstants.BACK_TAGS, referenceInfo.getBackExtTags());
        // 设置 lora 地址
        target.addExtInfo(CommonConstants.KEY_LORA_PATH, referenceInfo.getLoraPath());
        // 设置姿势场景 id
        target.addExtInfo(CommonConstants.KEY_POSE_ELEMENT_ID, referenceInfo.getLoraId());
        // 添加姿势示例图关键字
        target.addExtInfo(CommonConstants.KEY_BIZTAG, CommonConstants.POSE_SAMPLE_DIAGRAM);
    }

    /**
     * 填充参考图相关扩展信息
     *
     * @param target        任务
     * @param referenceInfo 参考图
     */
    private void populateTaskWithReferenceInfo(CreativeTaskVO target, AddCreativeRequest.ReferenceInfo referenceInfo) {
        // 是否是系统上传
        boolean isSystemUpload = Objects.nonNull(referenceInfo.getReferenceConfig());
        // 是否是用户上传参考图
        target.addExtInfo(IS_USER_UPLOAD_REFERENCE, !isSystemUpload);
        target.addExtInfo(KEY_MODEL_ID, referenceInfo.getLoraId());
        if (isSystemUpload) {
            target.addExtInfo(KEY_LENS, referenceInfo.getReferenceConfig().get(KEY_LENS));
            target.addExtInfo(KEY_POSTURE, referenceInfo.getReferenceConfig().get(KEY_POSTURE));
            target.addExtInfo(KEY_STYLE, referenceInfo.getReferenceConfig().get(KEY_STYLE));
        }
        target.addExtInfo(KEY_LORA_PATH, referenceInfo.getLoraPath());
        target.addExtInfo(BACK_TAGS, referenceInfo.getBackTags());
    }

    /**
     * 填充批次关联信息
     *
     * @param target 任务信息
     * @param batch  批次信息
     */
    private void populateTaskWithBatchInfo(CreativeTaskVO target, CreativeBatchVO batch) {
        target.setType(getCreativeType());
        // tryOn只出一张图片
        target.setBatchCnt(1);
        target.setImageProportion(batch.getImageProportion());
        // 设置提示词
        target.addExtInfo(PICTURE_MATTING_PROMPT, batch.getStringFromExtInfo(PICTURE_MATTING_PROMPT));
        // 设置是否需要换脸
        target.addExtInfo(IS_NEED_REPLACE_FACE, batch.getBooleanFromExtInfo(IS_NEED_REPLACE_FACE));
        // 设置服装类型
        target.addExtInfo(KEY_CLOTH_TYPE, batch.getStringFromExtInfo(CLOTHE_TYPE));
    }

    /**
     * 填充上传图片信息
     *
     * @param target            任务信息
     * @param batch             批次信息
     * @param referenceInfo     参考图信息
     * @param uploadedImageUrls 上传图片信息
     */
    private void populateTaskWithUploadedImages(CreativeTaskVO target, CreativeBatchVO batch,
                                                AddCreativeRequest.ReferenceInfo referenceInfo,
                                                UploadedImageUrls uploadedImageUrls) {
        // 设置参考图 URL
        target.addExtInfo(REFERENCE_ORIGINAL_IMAGE, referenceInfo.getImageUrl());
        target.addExtInfo(REFERENCE_IMAGE, referenceInfo.getUploadComfyUIImageUrl());
        target.addExtInfo(SHOW_CLOTHES_PIC, batch.getStringFromExtInfo(SHOW_CLOTHES_PIC_URL));
        target.addExtInfo(CLOTHE_IMAGE, uploadedImageUrls.getClotheImageUrl());
        target.addExtInfo(MASK_IMAGE_URL, batch.getStringFromExtInfo(MASK_IMAGE_URL));
        target.addExtInfo(MASK_IMAGE, uploadedImageUrls.getMaskImageUrl());
    }


    @Override
    protected String getFlowKey(List<CreativeElementVO> elements, MaterialModelVO modelVO, CreativeTaskVO task,
                                Map<String, Object> context) {
        // 是否需要替换人脸
        CreativeElementVO faceElement = (CreativeElementVO) context.get(ElementConfigKeyEnum.FACE.name());
        boolean isSwapFace = Objects.nonNull(faceElement) && faceElement.getId() != null;

        // 是否是用户上传的参考图
        boolean isUserUploadReference = Boolean.parseBoolean(
                task.getStringFromExtInfo(IS_USER_UPLOAD_REFERENCE));

        // 若 用户上传参考图 或 不换头 均走以下流程
        if (!isSwapFace || isUserUploadReference) {
            log.info("[ComfyUI流程][基础款换衣]BasicChangingClothesCreativeService::getFlowKey::执行用户上传参考图流程,taskId:{}", task.getId());
            return SystemConstants.USER_UPLOAD_REFERENCE_FLOW_PARAMS;
        }

        // 若换头且为系统Lora参考图 走以下流程
        log.info("[ComfyUI流程][基础款换衣]BasicChangingClothesCreativeService::getFlowKey::执行系统Lora参考图流程,taskId:{}", task.getId());
        return SystemConstants.SYSTEM_LORA_REFERENCE_FLOW_PARAMS;
    }

    /**
     * 重新检查服装信息
     *
     * @param modelVO 服装数据
     * @return 服装信息
     */
    protected MaterialModelVO reCheckModelVO(MaterialModelVO modelVO) {
        return modelVO == null ? mockModelVO() : modelVO;
    }


    /**
     * 数据修正
     *
     * @param elements   元素列表
     * @param modelVO    模型数据
     * @param context    配置map上下文
     * @param task       任务
     * @param promptSeed prompt的种子
     */
    protected void dataCorrect(List<CreativeElementVO> elements, MaterialModelVO modelVO, Map<String, Object> context,
                               CreativeTaskVO task, long promptSeed) {

        String swapType = StringUtils.EMPTY;

        // 初始化faceElement，避免重复深拷贝和空指针
        CreativeElementVO faceElement = null;

        // 若element为null则进行初始化
        if (elements == null) {
            elements = new ArrayList<>();
        }

        if (!elements.isEmpty()) {
            CreativeElementVO originFaceElement = elements.stream().filter(
                    element -> element.getConfigKey().equals(ElementConfigKeyEnum.FACE.name())).findFirst().orElse(null);

            if (originFaceElement != null) {
                faceElement = CommonUtil.deepCopy(originFaceElement);

                // lora模特，默认修脸强度1
                if (ElementUtils.isLoraFace(faceElement) && null == faceElement.getExtInfo(KEY_FACE_AFTER_STRENGTH,
                        Double.class)) {
                    faceElement.addExtInfo(KEY_FACE_AFTER_STRENGTH, 1);
                }

                // 获取 swapType
                swapType = Optional.ofNullable(faceElement)
                        .map(element -> element.getExtInfo("swapType", String.class))
                        .orElse("");
            }
        }

        // 如果faceElement为null，初始化一个空的对象
        if (faceElement == null) {
            faceElement = new CreativeElementVO();
            // 初始化扩展信息
            faceElement.setExtInfo(new JSONObject());
        }

        Integer loraId = task.getIntegerFromExtInfo(KEY_MODEL_ID);
        if (loraId != null) {
            CreativeElementVO sceneElement = creativeElementService.selectById(loraId);
            if (sceneElement != null) {
                // 添加场景元素
                elements.add(sceneElement);

                // 服装描述信息
                String styleOutfit = sceneElement.getExtInfo(KEY_STYLE_OUTFIT, String.class);
                // 服装类型
                String clothType = task.getStringFromExtInfo(KEY_CLOTH_TYPE);

                // 填充模型信息
                fillMockModel(modelVO, clothType, styleOutfit);

                context.put(ElementConfigKeyEnum.SCENE.name(), sceneElement);
            }
        }

        boolean isLoraFace = StringUtils.equals("LORA", swapType);
        context.put(IS_USE_LORA_FACE, isLoraFace);

        // 判断是否为 LORA 换脸（普通换脸情况下：isUseFacePic为true  lora 换脸：true为带照片，false 为不带照片）
        boolean isLoraSwapFace;
        if (isLoraFace) {
            String loraSwapFace = Optional.ofNullable(faceElement)
                    .map(element -> element.getExtInfo("loraSwapFace", String.class))
                    .orElse("");
            isLoraSwapFace = StringUtils.equals("Y", loraSwapFace);
        } else {
            isLoraSwapFace = true;
        }
        context.put(IS_USE_FACE_PIC, isLoraSwapFace);

        // 添加参考图片
        context.put(REFERENCE_IMAGE, task.getStringFromExtInfo(REFERENCE_IMAGE));

        // 设置扩展标签
        context.put(BACK_TAGS, task.getStringFromExtInfo(BACK_TAGS));
        // 是否需要替换人脸
        context.put(IS_NEED_REPLACE_FACE,
                task.getBooleanFromExtInfo(IS_NEED_REPLACE_FACE));
        // 是否是用户上传的参考图
        context.put(IS_USER_UPLOAD_REFERENCE,
                task.getExtValue(IS_USER_UPLOAD_REFERENCE, Boolean.class));
        // 设置服装信息
        context.put(STYLE_OUTFIT_INFO, task.getStringFromExtInfo(IS_USER_UPLOAD_REFERENCE));
        // 添加 衣服类型
        context.put(CLOTHE_TYPE, task.getStringFromExtInfo(KEY_CLOTH_TYPE));
        // 添加 衣服图片
        context.put(CLOTHE_IMAGE, task.getStringFromExtInfo(CLOTHE_IMAGE));
        // 添加 遮罩图片
        context.put(MASK_IMAGE, task.getStringFromExtInfo(MASK_IMAGE));
        // 设置服装描述词
        context.put(PICTURE_MATTING_PROMPT,
                task.getStringFromExtInfo(PICTURE_MATTING_PROMPT));

        // 设置随机 Seed
        context.put(KEY_SEED, String.valueOf(System.currentTimeMillis()));

        context.put("lora", modelVO);
        context.put(ElementConfigKeyEnum.FACE.name(), faceElement);
    }


    @Override
    public String correctFlow(String flow, Map<String, Object> context, CreativeTaskVO task, String flowKey) {
        return FlowUtils.correctFlow(flow, context);
    }


    @Override
    public void fillPreTaskResultKey(CreativeTaskVO creativeTaskVO, CreativeTypeEnum creativeType) {
        // 当前类型不关心传递进入的creativeType的值

        // 绘蛙前置任务返回结果为原始图片，需要设置在 REFERENCE_IMAGE
        creativeTaskVO.addExtInfo(KEY_PRE_TASK_RESULT_KEY, REFERENCE_IMAGE);
        // 目前只取一张结果图片
        creativeTaskVO.addExtInfo(KEY_PRE_TASK_RESULT_SIZE, 1);
    }

    /**
     * 获取图片抠图prompt
     *
     * @param request 请求体
     * @return 抠图prompt
     */
    private String getPictureMattingPrompt(AddCreativeRequest request) {

        // 获取图片抠图prompt
        if (Objects.nonNull(request.getMattingId())) {

            // 查询抠图任务信息
            List<CreativeTaskVO> creativeTaskVOS = creativeTaskService.queryTaskByBatchId(request.getMattingId());

            if (CollectionUtils.isNotEmpty(creativeTaskVOS)) {
                // 抠图任务只会有一个，此处取第一个
                CreativeTaskVO creativeTaskVO = creativeTaskVOS.get(0);

                // 获取文件服务器地址
                String fileServerUrl = serverHelper.getFileServerUrlByTask(creativeTaskVO);

                // 获取图片抠图prompt
                return comfyUIServiceImpl.fetchFileContent(comfyuiOutputPath + creativeTaskVO.getResultPath(),
                        ComfyUIUtils.buildFileNamePrefix(creativeTaskVO.getId()), "txt", fileServerUrl);
            }
        }

        return null;
    }


    /**
     * 模拟模型对象
     */
    private MaterialModelVO mockModelVO() {
        // 初始化服装模型信息
        MaterialModelVO mockModelVO = new MaterialModelVO();
        // 设置为Flux场景
        mockModelVO.setVersion(ModelVersionEnum.FLUX);
        // 设置状态
        mockModelVO.setStatus(MaterialModelStatusEnum.ENABLED.getCode());
        // 设置模型类型
        mockModelVO.setType(ModelTypeEnum.CUSTOM);

        // 返回服装信息
        return mockModelVO;
    }

    /**
     * 填充模拟model
     *
     * @param modelVO     模拟服装对象
     * @param clothType   服装类型
     * @param styleOutfit 服装描述
     */
    private void fillMockModel(MaterialModelVO modelVO, String clothType, String styleOutfit) {
        // 1、初始化训练参数
        LoraTrainDetail loraTrainDetail = new LoraTrainDetail();
        loraTrainDetail.setClothType(clothType);
        loraTrainDetail.setClothDir(StringUtils.EMPTY);

        // 2、初始化服装类型配置参数
        ClothTypeConfig clothTypeConfig = new ClothTypeConfig();

        // 使用ArrayList预设容量提升性能
        List<String> types = new ArrayList<>(3);
        // 设置为v2版本
        types.add(ModelVersionEnum.FLUX.getCode());
        // 默认为正面图
        types.add(CameraAngleEnum.FRONT_VIEW.getCode());

        // 使用contains优化判断逻辑
        if (clothType != null) {
            if (clothType.contains("upper")) {
                types.add(CameraAngleEnum.UPPER_BODY.getCode());
            } else if (clothType.contains("lower")) {
                types.add(CameraAngleEnum.LOWER_BODY.getCode());
            }
        }
        clothTypeConfig.setType(types);

        // 3、初始化颜色参数（使用单一对象避免创建集合）
        ClothColorDetail clothColorDetail = new ClothColorDetail();
        clothColorDetail.setIndex(1);
        clothColorDetail.setValue(styleOutfit);
        clothColorDetail.setViews(styleOutfit);

        // 直接使用Collections.singletonList减少内存分配
        clothTypeConfig.setColorList(Collections.singletonList(clothColorDetail));
        // 设置模型配置信息
        modelVO.setClothTypeConfigs(Collections.singletonList(clothTypeConfig));
        // 设置训练参数
        modelVO.setClothLoraTrainDetail(loraTrainDetail);
    }

    /**
     * 构建依赖任务（绘蛙任务 + 换头任务）
     *
     * @param data     任务信息
     * @param elements 场景模特信息
     * @return 内部出图任务进行返回
     */
    private List<CreativeTaskVO> buildMultiProcessTask(CreativeBatchVO batch, CreativeTaskVO data, List<CreativeElementVO> elements) {
        // 1、提取关键参数，是否需要换头
        Boolean isNeedReplaceFace = data.getBooleanFromExtInfo(IS_NEED_REPLACE_FACE);
        // 2、提取关键参数，是否是用系统参考图
        Boolean isUserUploadReference = data.getBooleanFromExtInfo(IS_USER_UPLOAD_REFERENCE);

        // 初始化结果
        List<CreativeTaskVO> resultList = new ArrayList<>();

        // 3、用户上传参考图任务构建（data 任务为主任务）
        if (isUserUploadReference) {
            for (int i = 0; i < 2; i++) {
                resultList.addAll(buildUserUploadMutiProcessTask(batch, data, elements, isNeedReplaceFace, i));
            }
            return resultList;
        }


        // 4、固定姿势重绘任务(结果用于 基础款换衣 和 绘蛙创作)
        // 4-1、创建重绘任务
        CreativeTaskVO createPoseTask = buildCreatePoseTask(data);
        // 4-2、更新基础款换衣任务
        updateMainTask(createPoseTask.getId(), data);
        // 4-3、创建换脸任务(该任务为主任务)
        CreativeTaskVO faceSwitchTask = buildFaceSwitchTask(batch, data, data.getId(), elements, YES, null);

        // 4-4、创建绘蛙任务
        for (int i = 0; i < 2; i++) {
            resultList.addAll(buildSystemUploadWithFaceProcessTask(batch, data, elements, createPoseTask, i, faceSwitchTask.getId()));
        }

        resultList.add(createPoseTask);
        resultList.add(faceSwitchTask);
        return resultList;
    }


    /**
     * 构建用户上传参考图多阶段任务<br>
     * <p>
     * 基础款换衣流程（无需固定姿势重绘）+绘蛙任务（无需固定姿势重绘）
     *
     * @param batch             批次信息
     * @param data              基础款换衣任务信息
     * @param elements          场景/模特 信息
     * @param isNeedReplaceFace 是否需要换头
     * @param index             索引信息
     * @return 多阶段任务列表
     */
    private List<CreativeTaskVO> buildUserUploadMutiProcessTask(CreativeBatchVO batch, CreativeTaskVO data, List<CreativeElementVO> elements, Boolean isNeedReplaceFace, Integer index) {
        // 1、构建绘蛙中间过程关联任务（无前置任务）
        CreativeTaskVO processTaskInsert = buildProcessTask(data, null, isNeedReplaceFace, Boolean.TRUE, index, data.getId());

        // 2、构建绘蛙任务 并 上传参考图
        buildHuiWaCommonTask(data, processTaskInsert.getId());

        // 3、若需要换头则额外创建换头任务
        CreativeTaskVO faceSwitchTask = null;
        if (isNeedReplaceFace) {
            // 4、构建换头任务
            faceSwitchTask = buildFaceSwitchTask(batch, data, processTaskInsert.getId(), elements, NO, data.getId());
            if (faceSwitchTask == null) {
                throw new BizException("【基础款换衣】二阶段模特换头任务创建失败！");
            }
        }

        // 5、返回新任务
        return Arrays.asList(processTaskInsert, faceSwitchTask);
    }

    /**
     * 构建系统选择参考图+换脸多阶段任务<br>
     * <p>
     * 基础款换衣流程（无需固定姿势重绘）+绘蛙任务（需要固定姿势重绘）
     *
     * @param batch          批次信息
     * @param data           基础款换衣信息
     * @param elements       场景/模特信息
     * @param createPoseTask 重绘任务
     * @param index          索引信息
     * @param mainTaskId     主任务 ID
     * @return 多阶段任务列表
     */
    private List<CreativeTaskVO> buildSystemUploadWithFaceProcessTask(CreativeBatchVO batch, CreativeTaskVO data, List<CreativeElementVO> elements, CreativeTaskVO createPoseTask, Integer index, Integer mainTaskId) {
        // 1、构建中间过程 Task 用于关联外部任务，该任务的结果根据情况判断是否需要添加进入 batch 中
        CreativeTaskVO processTaskInsert = buildProcessTask(data, createPoseTask.getId(), Boolean.TRUE, Boolean.FALSE, index, mainTaskId);

        // 2、更新 CommonTask 进行记录（不需要进行返回）
        buildCommonTask(data, processTaskInsert.getId());

        // 3、构建换头任务
        CreativeTaskVO faceSwitchTask = buildFaceSwitchTask(batch, data, processTaskInsert.getId(), elements,NO, mainTaskId);
        if (faceSwitchTask == null) {
            throw new BizException("【基础款换衣】二阶段模特换头任务创建失败！");
        }

        // 4、返回两个新任务
        return Arrays.asList(createPoseTask, processTaskInsert, faceSwitchTask);
    }


    /**
     * 上传参考图
     *
     * @param data 请求入参
     * @return 执行成功的任务 ID
     */
    private Long executeHuiwaUploadImage(CreativeTaskVO data) {
        // 调用绘蛙服务
        Long modelId = huiWaService.uploadPreferenceImage(data.getExtInfo(REFERENCE_ORIGINAL_IMAGE));

        // 若 imageTaskId 为空则说明绘蛙创建任务失败
        if (modelId == null) {
            throw new BizException("绘蛙上传参考图任务创建失败！");
        }

        return modelId;
    }

    /**
     * 构建 commonTask
     *
     * @param data           请求入参
     * @param creativeTaskId 对应批次 ID
     */
    private void buildCommonTask(CreativeTaskVO data, Integer creativeTaskId) {
        // 初始化 commonTask ，outTaskId 暂不填充
        CommonTaskVO commonTaskVO = new CommonTaskVO();
        commonTaskVO.setUserId(data.getUserId());
        commonTaskVO.setOperatorId(data.getOperatorId());
        commonTaskVO.setTaskType(CommonTaskEnums.TaskType.BASIC_POSTURE_CREATIVE.name());
        commonTaskVO.setTaskStatus(CommonTaskEnums.TaskStatus.INIT.name());
        commonTaskVO.setOutTaskPlatform(CommonTaskEnums.OutTaskPlatform.HUI_WA.name());
        commonTaskVO.setOutTaskStatus(CommonTaskEnums.TaskStatus.INIT.name());
        commonTaskVO.setRelatedBizId(String.valueOf(creativeTaskId));
        commonTaskVO.setRelatedBizType(CreativeTypeEnum.HUIWA_BASIC_CHANGING_CLOTHES.getCode());

        // 任务额外打标
        // 1、参考图是否已经上传完成，默认为 N
        commonTaskVO.addExtInfo(KEY_PREFERENCE_IS_UPLOAD_SUCCESS, NO);

        // 创建任务请求入参：服装类型
        commonTaskVO.addExtInfo(KEY_CLOTH_TYPE, data.getExtInfo(KEY_CLOTH_TYPE));
        // 创建任务请求入参：服装图片
        commonTaskVO.addExtInfo(SHOW_CLOTHES_PIC, data.getExtInfo(SHOW_CLOTHES_PIC));

        // 新增三方任务记录
        commonTaskService.insert(commonTaskVO);
    }


    /**
     * 构建创建姿势示例图任务
     *
     * @param data       固定姿势任务
     * @param facePrompt 模特提示词
     * @return 姿势示例图任务
     */
    private CreativeTaskVO buildCreatePoseTask(CreativeTaskVO data, String facePrompt) {
        CreativeTaskVO processTask = CommonUtil.deepCopy(data);
        // id重置为 null
        processTask.setId(null);
        // 任务类型重置为 姿势示例图
        processTask.setType(CreativeTypeEnum.POSE_SAMPLE_DIAGRAM);
        // 生成一张图片
        processTask.setBatchCnt(1);
        // 添加是否需要保存结果标识 （此处无论是否需要换脸都不需要存储结果）
        processTask.addExtInfo(KEY_IS_NEED_STORAGE_RESULT, Boolean.FALSE);
        // 添加多阶段任务标识
        processTask.addExtInfo(KEY_IS_MULTI_PROCESS, YES);
        // 此任务是正常任务，将标识置为 N
        processTask.addExtInfo(KEY_IS_THIRD_PART_TASK, NO);
        // 是否是主任务
        processTask.addExtInfo(KEY_IS_MAIN_TASK, NO);
        // 设置模特提示词
        if (StringUtils.isNotEmpty(facePrompt)) {
            processTask.addExtInfo(KEY_FACE_PROMPT, facePrompt);
        }


        // 设置 参考图上传至 ComfyUI 的地址
        processTask.addExtInfo(REFERENCE_IMAGE, data.getExtInfo(REFERENCE_IMAGE));
        // 设置参考图原始 url
        processTask.addExtInfo(REFERENCE_ORIGINAL_IMAGE, data.getExtInfo(REFERENCE_ORIGINAL_IMAGE));

        // 创建示例图特有参数
        processTask.addExtInfo(KEY_MODEL_ID, data.getExtInfo(KEY_MODEL_ID));
        processTask.addExtInfo(KEY_LENS, data.getExtInfo(KEY_LENS));
        processTask.addExtInfo(KEY_POSTURE, data.getExtInfo(KEY_POSTURE));
        processTask.addExtInfo(KEY_STYLE, data.getExtInfo(KEY_STYLE));
        processTask.addExtInfo(BACK_TAGS, data.getExtInfo(BACK_TAGS));
        processTask.addExtInfo(KEY_LORA_PATH, data.getExtInfo(KEY_LORA_PATH));
        processTask.addExtInfo(KEY_POSE_ELEMENT_ID, data.getExtInfo(KEY_POSE_ELEMENT_ID));
        processTask.addExtInfo(CommonConstants.KEY_BIZTAG, CommonConstants.POSE_SAMPLE_DIAGRAM);

        // 执行新增操作
        return creativeTaskService.insert(processTask);
    }

    /**
     * 构建创建姿势示例图任务
     *
     * @param data 固定姿势任务
     * @return 姿势示例图任务
     */
    private CreativeTaskVO buildCreatePoseTask(CreativeTaskVO data) {
        CreativeTaskVO processTask = CommonUtil.deepCopy(data);
        // id重置为 null
        processTask.setId(null);
        // 任务类型重置为 姿势示例图
        processTask.setType(CreativeTypeEnum.POSE_SAMPLE_DIAGRAM);
        // 生成一张图片
        processTask.setBatchCnt(1);
        // 添加是否需要保存结果标识 （此处无论是否需要换脸都不需要存储结果）
        processTask.addExtInfo(KEY_IS_NEED_STORAGE_RESULT, Boolean.FALSE);
        // 添加多阶段任务标识
        processTask.addExtInfo(KEY_IS_MULTI_PROCESS, YES);
        // 此任务是正常任务，将标识置为 N
        processTask.addExtInfo(KEY_IS_THIRD_PART_TASK, NO);
        // 是否是主任务
        processTask.addExtInfo(KEY_IS_MAIN_TASK, NO);

        // 设置 参考图上传至 ComfyUI 的地址
        processTask.addExtInfo(REFERENCE_IMAGE, data.getExtInfo(REFERENCE_IMAGE));
        // 设置参考图原始 url
        processTask.addExtInfo(REFERENCE_ORIGINAL_IMAGE, data.getExtInfo(REFERENCE_ORIGINAL_IMAGE));

        // 创建示例图特有参数
        processTask.addExtInfo(KEY_MODEL_ID, data.getExtInfo(KEY_MODEL_ID));
        processTask.addExtInfo(KEY_LENS, data.getExtInfo(KEY_LENS));
        processTask.addExtInfo(KEY_POSTURE, data.getExtInfo(KEY_POSTURE));
        processTask.addExtInfo(KEY_STYLE, data.getExtInfo(KEY_STYLE));
        processTask.addExtInfo(BACK_TAGS, data.getExtInfo(BACK_TAGS));
        processTask.addExtInfo(KEY_LORA_PATH, data.getExtInfo(KEY_LORA_PATH));
        processTask.addExtInfo(KEY_POSE_ELEMENT_ID, data.getExtInfo(KEY_POSE_ELEMENT_ID));
        processTask.addExtInfo(CommonConstants.KEY_BIZTAG, CommonConstants.POSE_SAMPLE_DIAGRAM);

        // 执行新增操作
        return creativeTaskService.insert(processTask);
    }


    /**
     * 构建中间过程Task ，用于关联外部任务<br>
     * 1、若换头，则该任务结果不需要添加进入 batch<br>
     * 2、若不换头则正常添加进入 batch<br>
     *
     * @param data                   请求入参
     * @param preTaskId              前置任务 ID
     * @param isNeedReplaceFace      是否需要换脸
     * @param isUploadHuiwaReference 是否已经上传绘蛙参考图
     * @param index                  索引信息
     * @param mainTaskId             主任务 ID
     * @return 新增完成的任务
     */
    private CreativeTaskVO buildProcessTask(CreativeTaskVO data, Integer preTaskId,
                                            Boolean isNeedReplaceFace, Boolean isUploadHuiwaReference,
                                            Integer index, Integer mainTaskId) {
        CreativeTaskVO processTask = CommonUtil.deepCopy(data);
        // id重置为 null
        processTask.setId(null);
        // 原始服装图片地址置空
        processTask.addExtInfo(SHOW_CLOTHES_PIC, null);
        // 原始服装 ComfyUI 路径置空
        processTask.addExtInfo(CLOTHE_IMAGE, null);

        // 任务类型重置为 绘蛙基础款换衣（任务同步时可根据该类型进行过滤）
        processTask.setType(CreativeTypeEnum.HUIWA_BASIC_CHANGING_CLOTHES);
        // 生成一张图片
        processTask.setBatchCnt(1);
        // 添加是否需要保存结果标识 （需要换脸时不保存，不需要换脸时进行保存）
        processTask.addExtInfo(KEY_IS_NEED_STORAGE_RESULT, !isNeedReplaceFace);
        // 添加多阶段任务标识
        processTask.addExtInfo(KEY_IS_MULTI_PROCESS, YES);
        // 添加三方任务标记(用于同步时进行任务分类)
        processTask.addExtInfo(KEY_IS_THIRD_PART_TASK, YES);
        // 是否是主任务
        processTask.addExtInfo(KEY_IS_MAIN_TASK, NO);
        // 是否已经上传绘蛙参考图
        processTask.addExtInfo(IS_UPLOAD_HUIWA_REFERENCE, isUploadHuiwaReference);

        // 是否需要转换为 ComfyUI 路径
        processTask.addExtInfo(KEY_IS_NEED_CHANGE_TO_COMFYUI_PATH, Boolean.FALSE);
        // 添加任务索引信息，用于后续获取绘蛙出图质量
        processTask.addExtInfo(KEY_TASK_INDEX, index);

        // 若存在前置任务时则添加如下参数信息
        if (preTaskId != null) {
            // 添加前置任务 ID
            processTask.setPreTaskId(preTaskId);

            // 固定姿势前置任务返回结果为原始图片，需要设置在CLOTHE_IMAGE
            processTask.addExtInfo(KEY_PRE_TASK_RESULT_KEY, CLOTHE_IMAGE);
            // 目前只取一张结果图片
            processTask.addExtInfo(KEY_PRE_TASK_RESULT_SIZE, 1);
        }

        // 当不换头时关联主TaskID
        if (!isNeedReplaceFace) {
            processTask.addExtInfo(KEY_MAIN_TASK_ID, mainTaskId);
        }

        // 执行新增操作
        return creativeTaskService.insert(processTask);
    }

    /**
     * 构建绘蛙任务 并 上传参考图
     *
     * @param data      基础款换衣任务信息
     * @param relatedId 关联任务 ID
     */
    private void buildHuiWaCommonTask(CreativeTaskVO data, Integer relatedId) {
        // 1、绘蛙上传参考图信息获取 ModelId
        Long modelId = executeHuiwaUploadImage(data);

        // 2、初始化 commonTask ，outTaskId 暂不填充
        CommonTaskVO commonTaskVO = new CommonTaskVO();
        commonTaskVO.setUserId(data.getUserId());
        commonTaskVO.setOperatorId(data.getOperatorId());
        commonTaskVO.setTaskType(CommonTaskEnums.TaskType.BASIC_POSTURE_CREATIVE.name());
        commonTaskVO.setTaskStatus(CommonTaskEnums.TaskStatus.INIT.name());
        commonTaskVO.setOutTaskPlatform(CommonTaskEnums.OutTaskPlatform.HUI_WA.name());
        commonTaskVO.setOutTaskStatus(CommonTaskEnums.TaskStatus.INIT.name());
        commonTaskVO.setRelatedBizId(String.valueOf(relatedId));
        commonTaskVO.setRelatedBizType(CreativeTypeEnum.HUIWA_BASIC_CHANGING_CLOTHES.getCode());

        // 3、 任务额外打标 参考图是否已经上传完成，默认为 N
        commonTaskVO.addExtInfo(KEY_PREFERENCE_IS_UPLOAD_SUCCESS, NO);
        // 创建任务请求入参：服装类型
        commonTaskVO.addExtInfo(KEY_CLOTH_TYPE, data.getExtInfo(KEY_CLOTH_TYPE));
        // 创建任务请求入参：服装图片
        commonTaskVO.addExtInfo(SHOW_CLOTHES_PIC, data.getExtInfo(SHOW_CLOTHES_PIC));
        // 创建任务请求入参：参考图 ID
        commonTaskVO.addExtInfo(KEY_PREFERENCE_MODEL_ID, modelId);

        // 新增三方任务记录
        commonTaskService.insert(commonTaskVO);
    }

    /**
     * 构建换脸任务
     *
     * @param batch         批次信息
     * @param data          任务信息
     * @param processTaskId 前置任务 ID
     * @param elements      场景模特信息
     * @param isMainTask    是否是主任务
     * @param mainTaskId    主任务 ID
     * @return 构建完成的换脸任务
     */
    private CreativeTaskVO buildFaceSwitchTask(CreativeBatchVO batch, CreativeTaskVO data, Integer processTaskId, List<CreativeElementVO> elements, String isMainTask, Integer mainTaskId) {
        // Mock 批次信息
        CreativeBatchVO creativeBatchVO = CommonUtil.deepCopy(batch);
        // 任务类型重置为换头
        creativeBatchVO.setType(CreativeTypeEnum.FACE_SCENE_SWITCH);
        // 生成一张图片
        creativeBatchVO.setBatchCnt(1);
        // 设置为多阶段任务
        creativeBatchVO.addExtInfo(KEY_IS_MULTI_PROCESS, YES);
        // 设置操作人 ID
        creativeBatchVO.setOperatorId(data.getOperatorId());

        // 2、构建换脸任务
        List<CreativeTaskVO> creativeTaskVOList = faceSceneSwitchCreativeService.buildTasks(creativeBatchVO, elements);
        if (creativeTaskVOList.isEmpty() || creativeTaskVOList.getFirst() == null) {
            log.error("【基础款换衣】【换脸任务】BasicChangingClothesService::buildFaceSwitchTask::换脸任务创建失败...");
            return null;
        }

        // 3、取出返回的任务进行数据订正
        CreativeTaskVO target = creativeTaskVOList.getFirst();
        // 构建 前置任务 id 参数
        target.setPreTaskId(processTaskId);
        // 设置扩展信息
        target.setExtInfo(creativeBatchVO.getExtInfo());
        // 设置额外的扩展信息
        target.addExtInfo(REFERENCE_ORIGINAL_IMAGE, data.getExtInfo(REFERENCE_ORIGINAL_IMAGE));
        // 添加多阶段任务标识
        target.addExtInfo(KEY_IS_MULTI_PROCESS, YES);
        // 添加是否需要保存结果标识
        target.addExtInfo(KEY_IS_NEED_STORAGE_RESULT, Boolean.TRUE);
        // 设置用户 ID 信息
        target.setUserId(data.getUserId());
        // 设置 ResultPath
        target.setResultPath(data.getResultPath());
        // 关联主 TaskID
        target.addExtInfo(KEY_MAIN_TASK_ID, mainTaskId);
        // 是否是主任务
        target.addExtInfo(KEY_IS_MAIN_TASK, isMainTask);
        // 是否需要转换为 ComfyUI 路径
        target.addExtInfo(KEY_IS_NEED_CHANGE_TO_COMFYUI_PATH, Boolean.TRUE);

        // 二次填充扩展信息
        faceSceneSwitchCreativeService.fillPreTaskResultKey(target, CreativeTypeEnum.FACE_SCENE_SWITCH);

        // 更新换头任务
        creativeTaskService.updateByIdSelective(target);

        // 返回任务
        return target;
    }

    /**
     * 更新主任务信息
     *
     * @param preTaskId 前置任务 ID
     * @param data      基础款换衣任务
     */
    private void updateMainTask(Integer preTaskId, CreativeTaskVO data) {
        // 1、清空参考图数据
        data.addExtInfo(REFERENCE_IMAGE, null);
        data.addExtInfo(REFERENCE_ORIGINAL_IMAGE, null);

        // 2、更新信息
        // 添加 多阶段任务 标识
        data.addExtInfo(KEY_IS_MULTI_PROCESS, YES);
        // 添加 需要存储结果 标识
        data.addExtInfo(KEY_IS_NEED_STORAGE_RESULT, Boolean.FALSE);
        // 任务设置前置任务
        data.setPreTaskId(preTaskId);
        // 添加 结果需要转换为ComfyUI路径 标识
        data.addExtInfo(KEY_IS_NEED_CHANGE_TO_COMFYUI_PATH, Boolean.TRUE);
        // 不需要缓存结果（不作为主任务）
        data.addExtInfo(KEY_IS_MAIN_TASK, NO);


        // 3、填充结果信息
        fillPreTaskResultKey(data, CreativeTypeEnum.BASIC_CHANGING_CLOTHES);

        // 3、执行更新操作
        creativeTaskService.updateByIdSelective(data);
    }


    @Data
    private static class UploadedImageUrls {
        // 衣服图片相对路径
        private final String clotheImageUrl;
        // mask图片相对路径
        private final String maskImageUrl;
    }


}
