package ai.conrain.aigc.platform.service.component.idempotent;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 基于内存的幂等性服务实现
 */
@Slf4j
@Service
@ConditionalOnProperty(name = "aigc.idempotent.type", havingValue = "memory", matchIfMissing = true)
public class MemoryIdempotentService implements IdempotentService {
    
    /**
     * 幂等性记录
     */
    private static class IdempotentRecord {
        private final LocalDateTime createdAt;
        private final LocalDateTime expiresAt;
        
        public IdempotentRecord(long ttlSeconds) {
            this.createdAt = LocalDateTime.now();
            this.expiresAt = this.createdAt.plusSeconds(ttlSeconds);
        }
        
        public boolean isExpired() {
            return LocalDateTime.now().isAfter(expiresAt);
        }
        
        public LocalDateTime getCreatedAt() { return createdAt; }
        public LocalDateTime getExpiresAt() { return expiresAt; }
    }
    
    /**
     * 存储幂等性记录的Map
     */
    private final ConcurrentHashMap<String, IdempotentRecord> records = new ConcurrentHashMap<>();
    
    /**
     * 定时清理任务
     */
    private final ScheduledExecutorService cleanupExecutor = Executors.newSingleThreadScheduledExecutor(r -> {
        Thread t = new Thread(r, "IdempotentCleanup");
        t.setDaemon(true);
        return t;
    });
    
    /**
     * 构造函数，启动定时清理任务
     */
    public MemoryIdempotentService() {
        // 每5分钟清理一次过期记录
        cleanupExecutor.scheduleAtFixedRate(this::cleanupExpiredRecords, 5, 5, TimeUnit.MINUTES);
        log.info("[MemoryIdempotentService] Initialized with periodic cleanup every 5 minutes");
    }
    
    @Override
    public boolean isAlreadyProcessed(String key) {
        if (key == null || key.trim().isEmpty()) {
            return false;
        }
        
        IdempotentRecord record = records.get(key);
        if (record == null) {
            return false;
        }
        
        // 检查是否过期
        if (record.isExpired()) {
            records.remove(key);
            return false;
        }
        
        return true;
    }
    
    @Override
    public void markAsProcessed(String key, long ttlSeconds) {
        if (key == null || key.trim().isEmpty()) {
            log.warn("[MemoryIdempotentService] Attempted to mark null or empty key as processed");
            return;
        }
        
        if (ttlSeconds <= 0) {
            ttlSeconds = 3600; // 默认1小时
        }
        
        IdempotentRecord record = new IdempotentRecord(ttlSeconds);
        records.put(key, record);
        
        log.debug("[MemoryIdempotentService] Marked key {} as processed, expires at {}", key, record.getExpiresAt());
    }
    
    @Override
    public Set<String> getAlreadyProcessedKeys(List<String> keys) {
        if (keys == null || keys.isEmpty()) {
            return Set.of();
        }
        
        return keys.stream()
                   .filter(this::isAlreadyProcessed)
                   .collect(Collectors.toSet());
    }
    
    @Override
    public void markAsProcessedBatch(List<String> keys, long ttlSeconds) {
        if (keys == null || keys.isEmpty()) {
            return;
        }
        
        if (ttlSeconds <= 0) {
            ttlSeconds = 3600; // 默认1小时
        }
        
        final long finalTtlSeconds = ttlSeconds;
        keys.stream()
            .filter(key -> key != null && !key.trim().isEmpty())
            .forEach(key -> markAsProcessed(key, finalTtlSeconds));
        
        log.debug("[MemoryIdempotentService] Batch marked {} keys as processed", keys.size());
    }
    
    @Override
    public void removeProcessedRecord(String key) {
        if (key == null || key.trim().isEmpty()) {
            return;
        }
        
        IdempotentRecord removed = records.remove(key);
        if (removed != null) {
            log.debug("[MemoryIdempotentService] Removed processed record for key: {}", key);
        }
    }
    
    @Override
    public void cleanupExpiredRecords() {
        int initialSize = records.size();
        
        records.entrySet().removeIf(entry -> {
            boolean expired = entry.getValue().isExpired();
            if (expired) {
                log.debug("[MemoryIdempotentService] Removing expired record for key: {}", entry.getKey());
            }
            return expired;
        });
        
        int finalSize = records.size();
        int removedCount = initialSize - finalSize;
        
        if (removedCount > 0) {
            log.info("[MemoryIdempotentService] Cleaned up {} expired records, remaining: {}", removedCount, finalSize);
        }
    }
    
    @Override
    public long getRecordCount() {
        return records.size();
    }
    
    /**
     * 获取所有记录的统计信息
     */
    public IdempotentStats getStats() {
        long totalRecords = records.size();
        long expiredRecords = records.values().stream()
                                     .mapToLong(record -> record.isExpired() ? 1 : 0)
                                     .sum();
        long activeRecords = totalRecords - expiredRecords;
        
        return new IdempotentStats(totalRecords, activeRecords, expiredRecords);
    }
    
    /**
     * 幂等性统计信息
     */
    public static class IdempotentStats {
        private final long totalRecords;
        private final long activeRecords;
        private final long expiredRecords;
        
        public IdempotentStats(long totalRecords, long activeRecords, long expiredRecords) {
            this.totalRecords = totalRecords;
            this.activeRecords = activeRecords;
            this.expiredRecords = expiredRecords;
        }
        
        public long getTotalRecords() { return totalRecords; }
        public long getActiveRecords() { return activeRecords; }
        public long getExpiredRecords() { return expiredRecords; }
        
        @Override
        public String toString() {
            return String.format("IdempotentStats{total=%d, active=%d, expired=%d}", 
                               totalRecords, activeRecords, expiredRecords);
        }
    }
    
    /**
     * 销毁方法，关闭定时任务
     */
    public void destroy() {
        cleanupExecutor.shutdown();
        try {
            if (!cleanupExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                cleanupExecutor.shutdownNow();
            }
        } catch (InterruptedException e) {
            cleanupExecutor.shutdownNow();
            Thread.currentThread().interrupt();
        }
        log.info("[MemoryIdempotentService] Cleanup executor shutdown completed");
    }
}
